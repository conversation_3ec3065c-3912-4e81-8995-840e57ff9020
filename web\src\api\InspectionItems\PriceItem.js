﻿import request from '@/utils/request'

// 价表字典项目类别
export function GetBillItemClassDict(query) {
  return request({
    url: '/PriceItem/GetBillItemClassDict',
    method: 'get',
    params: query
  })
}

// 价表字典维护

// 价表字典数据 维护列表
export function GetPriceItemNameDict(query) {
  return request({
    url: '/PriceItem/GetPriceItemNameDict',
    method: 'get',
    params: query
  })
}

//保存价表字典数据
export function AddPriceItem(data) {
  return request({
    url: '/PriceItem/AddPriceItem',
    method: 'post',
    data: data
  })
}


//更新价格项
export function UpdatePriceItem(query) {
  return request({
    url: '/PriceItem/UpdatePriceItem',
    method: 'get',
    params: query
  })
}

//停用价格项
export function DeletePriceItem(data) {
  return request({
    url: '/PriceItem/DeletePriceItem',
    method: 'post',
    data: data
  })
}


/**-----------------------价表----------------------------*/

//停用价格项
export function GetPriceList(query) {
  return request({
    url: '/PriceItem/GetPriceList',
    method: 'get',
    params: query
  })
}


//停用价格项
export function GetPriceListByItemCodeItemClass(query) {
  return request({
    url: '/PriceItem/GetPriceListByItemCodeItemClass',
    method: 'get',
    params: query
  })
}


//住院收费类别/门诊收费类别/核算项目类别/会计科目类别/病案费用类别
export function GetClassTypeList(query) {
  return request({
    url: '/PriceItem/GetClassTypeList',
    method: 'get',
    params: query
  })
}


//编辑保存价表到数据库
export function UpdatePriceList(data) {
  return request({
    url: '/PriceItem/UpdatePriceList',
    method: 'post',
    data: data
  })
}


//新增行到数据库
export function AddPriceList(data) {
  return request({
    url: '/PriceItem/AddPriceList',
    method: 'post',
    data: data
  })
}

//新增行
export function AddAllPriceList(query) {
  return request({
    url: '/PriceItem/AddAllPriceList',
    method: 'get',
    params: query
  })
}



export function DeletePriceNameDict(query) {
  return request({
    url: '/priceItemNameDict/DeletePriceNameDict',
    method: 'get',
    params: query
  })
}





