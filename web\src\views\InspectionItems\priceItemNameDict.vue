<template>
  <div>
    <el-card class="filter-card" shadow="never">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="80px">
        <el-form-item label="项目分类">
          <el-select v-model="queryParams.itemClass" placeholder="请选择项目分类" clearable @change="handleQuery"
                     style="width: 200px">
            <el-option
              v-for="item in billItemClassDictList"
              :key="item.classCode"
              :label="item.className"
              :value="item.classCode"/>
          </el-select>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model="queryParams.itemName" placeholder="请输入项目名称" clearable @input="handleQuery"
                    style="width: 200px"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button-group>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
<!--            <el-button type="success" icon="el-icon-plus" @click="handleAdd">新增</el-button>-->
          </el-button-group>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table :data="PriceItemNameList" stripe border class="main-table">
        <el-table-column type="index" label="序号" align="center" width="60"/>
        <el-table-column label="项目类型" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="billItemClassDictList.find(item => item.classCode === scope.row.iteM_CLASS)"
                    type="info" effect="dark" size="mini">
              {{ billItemClassDictList.find(item => item.classCode === scope.row.iteM_CLASS).className }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column property="iteM_NAME" label="项目名称" align="center" show-overflow-tooltip/>
        <el-table-column property="iteM_CODE" label="项目代码" align="center" show-overflow-tooltip/>
        <el-table-column property="inpuT_CODE" label="输入码" align="center" show-overflow-tooltip width="120"/>
        <el-table-column property="stoP_DATE_TIME" label="停用时间" align="center" show-overflow-tooltip width="160"/>
        <el-table-column label="操作" align="center" width="280">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" icon="el-icon-edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" @click="openStopDialog(scope.row)">停用</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination v-show="queryParams.total > 0"
                  :total="queryParams.total"
                  :page.sync="queryParams.pageNum"
                  :limit.sync="queryParams.pageSize"
                  @pagination="getList"/>
    </el-card>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="isEdit ? '编辑项目' : '新增项目'" :visible.sync="dialogVisible" width="50%"
               :close-on-click-modal="false">
      <el-form ref="form" :model="form" label-width="100px" :rules="rules" label-position="right">
        <el-row :gutter="30">
          <el-col :span="12">
            <el-form-item label="项目分类" prop="iteM_CLASS">
              <el-select v-model="form.iteM_CLASS" placeholder="请选择项目分类" style="width: 100%">
                <el-option
                  v-for="item in billItemClassDictList"
                  :key="item.classCode"
                  :label="item.className"
                  :value="item.classCode"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="iteM_NAME">
              <el-input v-model="form.iteM_NAME" @input="onNameInput" show-word-limit maxlength="50"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目代码" prop="iteM_CODE">
              <el-input v-model="form.iteM_CODE" show-word-limit maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="输入码">
              <el-input v-model="form.inpuT_CODE" autocomplete="off" placeholder="自动生成输入码" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" icon="el-icon-close">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" icon="el-icon-check">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 停用确认对话框 -->
    <el-dialog
      title="设置停用时间"
      :visible.sync="stopDialogVisible"
      width="30%"
      :before-close="handleCloseStopDialog">
      <el-form :model="stopForm" label-width="100px">
        <el-form-item label="停用时间">
          <el-date-picker
            v-model="stopForm.stopDate"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期时间"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="stopDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmStop">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  AddPriceItem, DeletePriceNameDict,
  GetBillItemClassDict,
  GetPriceItemNameDict,
  UpdatePriceItem
} from "../../api/InspectionItems/PriceItem";
import pinyin from "pinyin";
import {DeletePriceList} from "../../api/InspectionItems/JiaBiao";

export default {
  name: 'priceItemNameDict',
  props: [],
  components: {},
  data() {
    return {
      PriceItemNameList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        itemClass: '',
        itemName: ''
      },
      isEdit: false,
      form: {
        id: null,
        iteM_CLASS: '',
        iteM_NAME: '',
        iteM_CODE: '',
        inpuT_CODE: '',
        tempItemName: '',
        tempItemCode: ''
      },
      dialogVisible: false,
      billItemClassDictList: [],
      tempItemName: '',
      tempItemCode: '',
      stopForm: {
        stopDate: new Date().toISOString().slice(0, 19).replace('T', ' ') // 默认当前时间
      },
      stopDialogVisible: false, // 控制停用对话框显示
      rules: {
        iteM_CLASS: [
          {required: true, message: '请选择项目分类', trigger: 'change'}
        ],
        iteM_NAME: [
          {required: true, message: '请输入项目名称', trigger: 'blur'},
          {min: 1, max: 50, message: '项目名称长度在1到50个字符之间', trigger: 'blur'}
        ],
        iteM_CODE: [
          {required: true, message: '请输入项目代码', trigger: 'blur'},
          {min: 1, max: 20, message: '项目代码长度在1到20个字符之间', trigger: 'blur'},
          {pattern: /^[A-Za-z0-9]+$/, message: '项目代码只能包含字母和数字', trigger: 'blur'}
        ]
      },
    }
  },
  created() {
    this.getDict()
  },
  mounted() {
  },
  methods: {
    getDict() {
      GetBillItemClassDict().then(res => {
        this.billItemClassDictList = res.data
        this.getList()
      })
    },
    getList() {
      GetPriceItemNameDict(this.queryParams).then(res => {
        this.PriceItemNameList = res.data.rows
        this.queryParams.total = res.data.total
      })
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    handleAdd() {
      this.isEdit = false;
      this.form = {
        id: null,
        iteM_CLASS: '',
        iteM_NAME: '',
        iteM_CODE: '',
        inpuT_CODE: ''
      };
      this.dialogVisible = true;
    },
    // 项目名称输入自动生成输入码
    onNameInput(val) {
      if (val) {
        // 取首字母大写输入码
        const pyArr = pinyin(val, {style: pinyin.STYLE_FIRST_LETTER})
        this.form.inpuT_CODE = pyArr.map(arr => arr[0]).join('').toUpperCase()
      } else {
        this.form.inpuT_CODE = ''
      }
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.isEdit) {
            // 更新操作
            this.form.tempItemName = this.tempItemName;
            this.form.tempItemCode = this.tempItemCode;
            UpdatePriceItem(this.form).then(() => {
              // this.$message.success('更新成功');
              this.dialogVisible = false;
              this.getList();
            }).catch(error => {
              console.error('更新失败:', error);
              this.$message.error('更新失败');
            });
          } else {
            // 新增操作
            AddPriceItem(this.form).then(() => {
              this.$message.success('新增成功');
              this.dialogVisible = false;
              this.getList();
            }).catch(error => {
              console.error('新增失败:', error);
              this.$message.error('新增失败');
            });
          }
        } else {
          this.$message.error('请完善表单信息');
          return false;
        }
      });
    },

    handleEdit(row) {
      this.tempItemName = row.iteM_NAME;
      this.tempItemCode = row.iteM_CODE;
      this.isEdit = true;
      this.form = {...row};
      this.dialogVisible = true;
    },

    // 打开停用对话框
    openStopDialog(row) {
      this.currentRow = row;
      const now = new Date();
      const timezoneOffset = now.getTimezoneOffset() * 60000; // 获取时区偏移（毫秒）
      const localNow = new Date(now - timezoneOffset); // 调整为本地时间
      this.stopForm.stopDate = localNow.toISOString().slice(0, 19).replace('T', ' '); // 设置默认时间为当前时间
      this.stopDialogVisible = true;
    },
    // 关闭停用对话框
    handleCloseStopDialog(done) {
      this.stopDialogVisible = false;
      done();
    },

    // 确认停用操作
    confirmStop() {
      if (!this.currentRow) {
        this.$message.error("没有选中的数据");
        return;
      }

      const temp = {
        iteM_CLASS: this.currentRow.iteM_CLASS,
        iteM_NAME: this.currentRow.iteM_NAME,
        starT_DATE: this.currentRow.starT_DATE_TIME,
        stoP_DATE: this.stopForm.stopDate // 添加停用时间
      };

      this.$confirm('是否确认停用该数据项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DeletePriceNameDict(temp).then(() => {
          this.$message.success('停用成功');
          this.getList();
        }).catch(error => {
          console.error('停用失败：', error);
          this.$message.error('停用失败');
        });
      }).catch(() => {
        this.$message.info('已取消停用');
      });
      this.stopDialogVisible = false;
    },
  },
}
</script>

<style scoped>

</style>

