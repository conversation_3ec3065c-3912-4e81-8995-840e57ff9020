<template>
    <!-- 检查子类维护 -->
    <div class="inherit-platform">
        <div class="page-header">
            <div class="left">
                <div class="title">检查类别维护</div>
                <el-input v-model="searchKeyword" placeholder="请输入项目名称" prefix-icon="el-icon-search" @input="handleSearch"
                    clearable></el-input>
            </div>
            <div class="right">
    
                <el-row :gutter="10">
    
    
                    <el-button type="primary" @click="getList()">刷新</el-button>
    
                    <el-button type="primary" @click="toAdd()">新增</el-button>
    
                </el-row>
            </div>
        </div>
    
        <el-table v-loading="loading" :data="dateList" border stripe highlight-current-row style="width: 100%"
            height="600px" :default-sort="{prop: 'datA_ORDER', order: 'ascending'}">
            <el-table-column type="index" label="序号" align="center" />
            <el-table-column label="项目类别code" align="center" prop="exaM_CLASS_CODE" />
            <el-table-column label="项目类别名称" align="center" prop="exaM_CLASS_NAME" />
            <el-table-column label="简拼" align="center" prop="inpuT_CODE" />
            <el-table-column label="科室" align="center" prop="perforM_BY" />
            <el-table-column label="打印单样式" align="center" prop="prinT_STYLE" />
            <el-table-column label="本地编号类别" align="center" prop="loacaL_ID_CLASS" />
    
            <el-table-column label="科室位置" align="center" prop="exaM_DEPT_POSITION" />
            <el-table-column label="设备类型" align="center" prop="devicE_TYPE" />
            <el-table-column label="设备名称" align="center" prop="devicE_NAME" />
            <el-table-column label="报告公开" align="center" prop="reporT_OPEN" />
            <el-table-column label="排序" align="center" prop="datA_ORDER" />
    
    
            <el-table-column label="操作" align="center" fixed="right">
                <template slot-scope="scope">
                    <el-button size="mini" type="primary" icon="el-icon-edit" plain
                        @click="handleEdit(scope.row)">编辑</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <div class="empty-box">
                    <img :src="emptyImg" alt="暂无数据" class="empty-img" />
                    <div class="empty-text">暂无数据</div>
                </div>
            </template>
        </el-table>
        <!-- 分页 -->
    
    
    
        <!-- 新增项目 -->
        <el-dialog title="新增类别" :visible.sync="openAddDialog" width="900px" top="5vh" :modal-append-to-body="false"
            :close-on-click-modal="false" class="custom-add-dialog">
            <!-- 上方表单 -->
            <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="90px" label-position="top"
                class="form-section">
    
                <div class="form-row">
    
    
                    <el-form-item label="检查类别名称" prop="exaM_CLASS_NAME">
                        <el-input v-model="addForm.exaM_CLASS_NAME" placeholder="请输入检查类别名称" clearable
                            @input="onNameInput" />
                    </el-form-item>
                    <el-form-item label="检查类别代码" prop="exaM_CLASS_CODE">
                        <el-input v-model="addForm.exaM_CLASS_CODE" placeholder="请输入检查类别代码" clearable />
                    </el-form-item>
    
                </div>
    
                <div class="form-row">
                    <el-form-item label="输入码" prop="inpuT_CODE">
                        <el-input v-model="addForm.inpuT_CODE" placeholder="请输入输入码" clearable />
                    </el-form-item>
                    <el-form-item label="检查部门" prop="perforM_BY">
    
                        <el-select v-model="addForm.perforM_BY" placeholder="请输入检查部门代码"><el-option
                                v-for="(item,index) in searchList" :key="index" :label="item.deptname"
                                :value="item.deptcode" clearable />
                        </el-select>
    
                    </el-form-item>
    
    
                </div>
                <div class="form-row">
                    <el-form-item label="打印单样式" prop="prinT_STYLE">
                        <el-input v-model="addForm.prinT_STYLE" placeholder="请输入打印单样式" clearable />
                    </el-form-item>
    
    
    
                </div>
                <div class="form-row">
                    <el-form-item label="本地编号类别" prop="loacaL_ID_CLASS">
                        <el-input v-model="addForm.loacaL_ID_CLASS" placeholder="请输入本地编号类别" clearable />
                    </el-form-item>
    
    
    
                </div>
                <div class="form-row">
                    <el-form-item label="科室位置" prop="exaM_DEPT_POSITION">
                        <el-input v-model="addForm.exaM_DEPT_POSITION" placeholder="请输入科室位置" clearable />
                    </el-form-item>
    
                    <el-form-item label="设备类型" prop="devicE_TYPE">
                        <el-input v-model="addForm.devicE_TYPE" placeholder="请输入设备类型" clearable />
                    </el-form-item>
                </div>
                <div class="form-row">
                    <el-form-item label="设备名称" prop="devicE_NAME">
                        <el-input v-model="addForm.devicE_NAME" placeholder="请输入设备名称" clearable />
                    </el-form-item>
    
                    <el-form-item label="报告公开(Y/N)" prop="reporT_OPEN">
                        <el-select v-model="addForm.reporT_OPEN" placeholder="请选择">
                            <el-option label="对个人公开" value="Y" />
                            <el-option label="对个人保护" value="N" />
                        </el-select>
                    </el-form-item>
                </div>
                <div class="form-row">
                    <el-form-item label="显示排序" prop="datA_ORDER">
                        <el-input-number v-model="addForm.datA_ORDER" :min="0" />
                    </el-form-item>
    
    
                </div>
            </el-form>
            <!-- 新增项目按钮 -->
            <div slot="footer">
                <el-button @click="openAddDialog = false">取消</el-button>
                <el-button type="primary" @click="handleAdd">确定</el-button>
            </div>
        </el-dialog>
        <!-- 修改项目 -->
        <el-dialog title="类别维护" :visible.sync="openUpdateDialog" width="900px" top="5vh" :modal-append-to-body="false"
            :close-on-click-modal="false" class="custom-add-dialog">
            <!-- 上方表单 -->
            <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="90px" label-position="top"
                class="form-section">
                <div class="form-row">
    
    
                    <el-form-item label="检查类别名称" prop="exaM_CLASS_NAME">
                        <el-input v-model="addForm.exaM_CLASS_NAME" placeholder="请输入检查类别名称" clearable
                            @input="onNameInput" />
                    </el-form-item>
                    <el-form-item label="检查类别代码" prop="exaM_CLASS_CODE">
                        <el-input v-model="addForm.exaM_CLASS_CODE" placeholder="请输入检查类别代码" clearable />
                    </el-form-item>
    
                </div>
    
                <div class="form-row">
                    <el-form-item label="输入码" prop="inpuT_CODE">
                        <el-input v-model="addForm.inpuT_CODE" placeholder="请输入输入码" clearable />
                    </el-form-item>
                    <el-form-item label="检查部门" prop="perforM_BY">
                        <el-select v-model="addForm.perforM_BY" placeholder="请输入检查部门代码"><el-option
                                v-for="(item,index) in searchList" :key="index" :label="item.deptname"
                                :value="item.deptcode" clearable />
                        </el-select>
                    </el-form-item>
    
    
                </div>
                <div class="form-row">
                    <el-form-item label="打印单样式" prop="prinT_STYLE">
                        <el-input v-model="addForm.prinT_STYLE" placeholder="请输入打印单样式" clearable />
                    </el-form-item>
    
    
    
                </div>
                <div class="form-row">
                    <el-form-item label="本地编号类别" prop="loacaL_ID_CLASS">
                        <el-input v-model="addForm.loacaL_ID_CLASS" placeholder="请输入本地编号类别" clearable />
                    </el-form-item>
    
    
    
                </div>
                <div class="form-row">
                    <el-form-item label="科室位置" prop="exaM_DEPT_POSITION">
                        <el-input v-model="addForm.exaM_DEPT_POSITION" placeholder="请输入科室位置" clearable />
                    </el-form-item>
    
                    <el-form-item label="设备类型" prop="devicE_TYPE">
                        <el-input v-model="addForm.devicE_TYPE" placeholder="请输入设备类型" clearable />
                    </el-form-item>
                </div>
                <div class="form-row">
                    <el-form-item label="设备名称" prop="devicE_NAME">
                        <el-input v-model="addForm.devicE_NAME" placeholder="请输入设备名称" clearable />
                    </el-form-item>
    
                    <el-form-item label="报告公开(Y/N)" prop="reporT_OPEN">
                        <el-select v-model="addForm.reporT_OPEN" placeholder="请选择">
                            <el-option label="对个人公开" value="Y" />
                            <el-option label="对个人保护" value="N" />
                        </el-select>
                    </el-form-item>
                </div>
                <div class="form-row">
                    <el-form-item label="显示排序" prop="datA_ORDER">
                        <el-input-number v-model="addForm.datA_ORDER" :min="0" />
                    </el-form-item>
                    <el-form-item label="状态" prop="statuS">
                        <el-select v-model="addForm.statuS" placeholder="请选择状态">
                            <el-option label="正常" value="0" />
                            <el-option label="停用" value="1" />
                            <el-option label="删除" value="-1" />
                        </el-select>
                    </el-form-item>
    
                </div>
    
            </el-form>
            <!-- 确定项目按钮 -->
            <div slot="footer">
                <el-button @click="openUpdateDialog = false">取消</el-button>
                <el-button type="primary" @click="handleUpdate">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
  GetSelectDiagDict, GetExamDict, AddExamDict, UpdateExamDict, GetDeptDict
} from "@/api/examPattern/index"
import pinyin from 'pinyin'

export default {
  name: 'Index',
  data() {
    return {
      searchKeyword: '',
      selectDiag: [],
      searchList: [],
      stasus: '',
      loading: false,
      dateList: [],
      emptyImg: require('@/assets/images/1.png'),
      openUpdateDialog: false,
      openAddDialog: false,
      diagnosisDict: [],
      addForm: {
        exaM_CLASS_NAME: '',     // 检查类别名称
        exaM_CLASS_CODE: '',     // 检查类别代码
        inpuT_CODE: '',          // 输入码
        perforM_BY: '',          // 检查部门代码
        prinT_STYLE: '',         // 打印单样式
        specialtieS_DEPT: null,  // 是否按样式单打印
        loacaL_ID_CLASS: '',     // 本地编号类别
        macH_NUM: null,          // 显示打印（机器数量）
        exaM_DEPT_POSITION: '',  // 科室位置
        devicE_TYPE: '',         // 设备类型
        devicE_NAME: '',         // 设备名称
        reporT_OPEN: 'Y',        // 报告公开
        datA_ORDER: null,        // 排序
        statuS: '0'              // 状态
      },
      addRules: {

      },
      queryParams: {
        inputCode: undefined,
        pageSize: 10,
        pageNum: 1,
      },
      total: 0,
      examDict: [],
    }
  },

  computed: {

  },

  created() {
    this.getList()
    this.getSelect()
  },

  methods: {
    getSelect() {

      try {
        GetDeptDict(this.queryParams).then(res => {
          if (res && res.data) {
            this.searchList = res.data

          }
        })
      } catch (error) {
        this.$message.error('获取列表失败')
      }


    },
    handleChange(value) {
      console.log(value);
      this.addForm.exaM_CLASS = value[0]
      this.addForm.exaM_SUB_CLASS = value[1]
    },
    handleSelectChange(value) {
      if (value) {
        const selectedItem = this.selectDiag.find(item => item.iteM_NAME === value);
        if (selectedItem) {
          this.addForm.description = selectedItem.iteM_NAME;
          this.addForm.descriptioN_CODE = selectedItem.iteM_CODE;
          this.addForm.descriptioN_CODE = selectedItem.iteM_CODE;
          this.addForm.inpuT_CODE = selectedItem.inpuT_CODE;
        }
      } else {
        // 当清空选择时，重置两个字段
        this.addForm.description = '';
        this.addForm.descriptioN_CODE = '';
        this.addForm.inpuT_CODE = ''
      }
    },
    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        GetExamDict(this.queryParams).then(res => {
          if (res && res.data) {
            this.dateList = res.data

          }
        })
      } catch (error) {
        this.$message.error('获取列表失败')
      }
      this.loading = false
    },
    handleSearch() {
      if (this.searchKeyword) {
        this.dateList = this.dateList.filter(item =>
          item.exaM_CLASS_NAME.toLowerCase().includes(this.searchKeyword.toLowerCase())
          //  || item.inpuT_CODE.toLowerCase().includes(this.searchKeyword.toLowerCase())

        );
      } else {
        this.getList(); // 清空搜索时重新加载全部数据
      }
    },
    // 项目名称输入自动生成拼音码
    onNameInput(val) {
      if (val) {
        // 取首字母大写拼音码
        const pyArr = pinyin(val, { style: pinyin.STYLE_FIRST_LETTER })
        this.addForm.inpuT_CODE = pyArr.map(arr => arr[0]).join('').toUpperCase()
      } else {
        this.addForm.inpuT_CODE = ''
      }

    },

    handleSizeChange(val) {

      this.queryParams.pageSize = val;
      this.getList();

    },
    handleCurrentChange(val) {

      this.queryParams.pageNum = val;
      this.getList();

    },
    toAdd() {
      this.stasus = 'add'
      this.addForm = {
        exaM_CLASS: undefined,
        exaM_SUB_CLASS: undefined,
        desC_ITEM: undefined,
        desC_NAME: undefined,
        description: undefined,
        descriptioN_CODE: undefined,
        inpuT_CODE: undefined,
        inpuT_CODE_WB: undefined,
        exaM_NOTICE: undefined,
        pB_SUB_CLASS: undefined,
        exaM_POSITION_CODE: undefined,
        exaM_POSITION_NAME: undefined,
        starT_DATE_TIME: undefined,
        stoP_DATE_TIME: undefined,
        mergE_GROUP: undefined,
        repeL_GROUP: undefined,
      },
        this.openAddDialog = true
    },
    // 新增项目
    handleAdd() {
      console.log(this.addForm);
      this.$refs.addFormRef.validate(valid => {
        if (!valid) return
        // 这里可以调用AddDiagnosis接口进行保存
        AddExamDict(this.addForm).then(res => {
          if (res && res.code === 200) {
            this.$message.success('新增成功')
            this.openAddDialog = false
            this.getList && this.getList()
          } else {
            this.$message.error(res.msg || '新增失败')
          }
        })
      })
    },




    // 编辑项目
    handleEdit(row) {
      // 将表格行数据填充到表单中
      //   this.addForm = {
      //     exaM_CLASS: row.exaM_CLASS,
      //     exaM_SUB_CLASS: row.exaM_SUB_CLASS,
      //     desC_ITEM: row.desC_ITEM,
      //     desC_NAME: row.desC_NAME,
      //     description: row.description,
      //     descriptioN_CODE: row.descriptioN_CODE,
      //     inpuT_CODE: row.inpuT_CODE,
      //     inpuT_CODE_WB: row.inpuT_CODE_WB,
      //     exaM_NOTICE: row.exaM_NOTICE,
      //     pB_SUB_CLASS: row.pB_SUB_CLASS,
      //     exaM_POSITION_CODE: row.exaM_POSITION_CODE,
      //     exaM_POSITION_NAME: row.exaM_POSITION_NAME,
      //     starT_DATE_TIME: row.starT_DATE_TIME,
      //     stoP_DATE_TIME: row.stoP_DATE_TIME,
      //     mergE_GROUP: row.mergE_GROUP,
      //     repeL_GROUP: row.repeL_GROUP,
      //     }
      this.stasus = 'update'
      this.addForm = { ...this.addForm, ...row }
      this.openUpdateDialog = true
    },
    handleUpdate() {
      this.$refs.addFormRef.validate(valid => {
        if (!valid) return
        // 这里可以调用AddDiagnosis接口进行保存
        UpdateExamDict(this.addForm).then(res => {
          if (res && res.code === 200) {
            this.$message.success('操作成功')
            this.openUpdateDialog = false
            this.getList && this.getList()
          } else {
            this.$message.error(res.msg || '操作成功')
          }
        })
      })
    },

  }
}
</script>

<style lang="scss" scoped>
// 主容器样式
.inherit-platform {
    padding: 24px;
    background-color: #f6f8fa;
    min-height: calc(100vh - 84px);
}

// 页面头部样式
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 0 8px;

    .left {
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2329;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 14px;
            color: #86909c;
        }
    }

    .right {
        .el-button {
            padding: 12px 24px;
            font-size: 14px;
            border-radius: 8px;
            background: #409eff;
            border: none;
            color: #fff;
            transition: all 0.3s;

            &:hover {
                background: #66b1ff;
                box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
            }
        }
    }
}

// 空数据样式
.empty-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    .empty-img {
        width: 80px;
        height: 80px;
        margin-bottom: 12px;
        opacity: 0.7;
    }

    .empty-text {
        color: #999;
        font-size: 16px;
    }
}

.custom-add-dialog .el-dialog {
    border-radius: 14px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
}

.custom-add-dialog .el-dialog__header {
    background: #f5f7fa;
    border-radius: 14px 14px 0 0;
    padding: 18px 24px 10px 24px;
}

.custom-add-dialog .el-dialog__body {
    padding: 24px 32px 10px 32px;
    background: #fff;
}

.custom-add-dialog .el-form-item {
    margin-bottom: 22px;
}

.custom-add-dialog .el-input__inner {
    border-radius: 7px;
    height: 38px;
    font-size: 15px;
}

.custom-add-dialog .el-select .el-input__inner {
    border-radius: 7px;
}

.custom-add-dialog .el-dialog__footer {
    padding: 12px 32px 24px 32px;
    background: #f5f7fa;
    border-radius: 0 0 14px 14px;
    text-align: right;
}

.custom-add-dialog .el-button {
    min-width: 80px;
    border-radius: 7px;
    font-size: 15px;
    margin-left: 12px;
}

// 表单区域样式
.form-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.form-row {
    display: flex;
    gap: 10px;
    margin-bottom: 1px;
}

.form-row .el-form-item {
    flex: 1;
    margin-bottom: 0;
}

.form-section .el-form-item {
    margin-bottom: 16px;
}

.form-section .el-input__inner,
.form-section .el-select .el-input__inner {
    border-radius: 6px;
    height: 36px;
    font-size: 14px;
}

// 表格区域样式
.table-section {
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.table-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2329;
}

.table-count {
    font-size: 14px;
    color: #86909c;
}

.table-section .el-table {
    border: none;
}

.table-section .el-table th {
    background: #f8f9fa;
    color: #1f2329;
    font-weight: 600;
}

.table-section .el-table td {
    padding: 8px 0;
}
</style>