<template>
  <div>
    <!-- 查询表单 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="80px">
        <el-form-item label="项目分类">
          <el-select v-model="queryParams.itemClass" placeholder="请选择项目分类" clearable @change="handleQuery"
                     style="width: 200px">
            <el-option
              v-for="item in billItemClassDictList"
              :key="item.classCode"
              :label="item.className"
              :value="item.classCode"/>
          </el-select>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model="queryParams.itemName" placeholder="请输入项目名称" clearable @input="handleQuery"
                    style="width: 200px"></el-input>
        </el-form-item>
        <el-form-item label="输入码">
          <el-input v-model="queryParams.inputCode" placeholder="请输入输入码" clearable @input="handleQuery"
                    style="width: 200px"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button-group>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button type="success" icon="el-icon-plus" @click="addPriceRow">新增</el-button>
          </el-button-group>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table :data="JiaBiaoList" stripe border class="main-table">
        <el-table-column type="index" label="序号" align="center" width="60"/>
        <el-table-column label="项目类别" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="billItemClassDictList.find(item => item.classCode === scope.row.iteM_CLASS)"
                    type="info" effect="dark" size="mini">
              {{ billItemClassDictList.find(item => item.classCode === scope.row.iteM_CLASS).className }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column property="iteM_CODE" label="项目代码" align="center" show-overflow-tooltip/>
        <el-table-column property="iteM_NAME" label="项目名称" align="center" show-overflow-tooltip/>
        <el-table-column property="iteM_SPEC" label="项目规格" align="center" show-overflow-tooltip/>
        <el-table-column property="units" label="单位" align="center" show-overflow-tooltip/>
        <el-table-column property="price" label="价格" align="center" show-overflow-tooltip/>
        <el-table-column property="starT_DATE" label="开始日期" align="center" show-overflow-tooltip/>
        <el-table-column property="stoP_DATE" label="停用日期" align="center" show-overflow-tooltip/>
        <el-table-column label="操作" align="center" width="550">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" icon="el-icon-edit" @click="openPriceDetailDrawer(scope.row)">编辑
            </el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" @click="openStopDialog(scope.row)">停用</el-button>
            <el-button size="mini" type="warning" icon="el-icon-delete" @click="openPriceMaintenance(scope.row)">
              价表名称维护
            </el-button>
            <el-button size="mini" type="warning" icon="el-icon-delete" @click="GetZhenLiaoProject(scope.row)">生成诊疗项目
            </el-button>
            <el-button size="mini" type="warning" icon="el-icon-delete" @click="GetDuiZhao(scope.row)">生成对照</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination v-show="queryParams.total > 0"
                  :total="queryParams.total"
                  :page.sync="queryParams.pageNum"
                  :limit.sync="queryParams.pageSize"
                  @pagination="getList"/>
    </el-card>

    <!-- 停用确认对话框 -->
    <el-dialog
      title="设置停用时间"
      :visible.sync="stopDialogVisible"
      width="30%"
      :before-close="handleCloseStopDialog">
      <el-form :model="stopForm" label-width="100px">
        <el-form-item label="停用时间">
          <el-date-picker
            v-model="stopForm.stopDate"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期时间"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否同步操作">
          <el-checkbox v-model="stopForm.syncZhenLiao">同步停用诊疗项目</el-checkbox>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="stopDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmStop">确 定</el-button>
      </span>
    </el-dialog>

    <!--    编辑价表详细信息抽屉-->
    <el-drawer
      title="编辑价表详细信息"
      :visible.sync="editPriceDetailDrawerVisible"
      direction="rtl"
      size="83%"
      :close-on-click-modal="false"
    >
      <div style="padding: 20px;">
        <el-form :model="editPriceDetailForm" label-width="120px" label-position="right">
          <el-row :gutter="20">
            <!-- 基础信息 -->
            <el-col :span="8">
              <el-form-item label="项目类别">
                <el-select v-model="editPriceDetailForm.iteM_CLASS" placeholder="请选择项目类别">
                  <el-option
                    v-for="item in billItemClassDictList"
                    :key="item.classCode"
                    :label="item.className"
                    :value="item.classCode"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目代码">
                <el-input v-model="editPriceDetailForm.iteM_CODE"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目名称">
                <el-input v-model="editPriceDetailForm.iteM_NAME"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目规格">
                <el-input v-model="editPriceDetailForm.iteM_SPEC"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单位">
                <el-input v-model="editPriceDetailForm.units"/>
              </el-form-item>
            </el-col>

            <!-- 价格信息 -->
            <el-col :span="6">
              <el-form-item label="价格">
                <el-input-number v-model.number="editPriceDetailForm.price" :precision="2" :step="0.1"
                                 style="width: 100%"
                                 disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="优惠价格">
                <el-input-number v-model.number="editPriceDetailForm.prefeR_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="外宾价格">
                <el-input-number v-model.number="editPriceDetailForm.foreigneR_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="最高限价">
                <el-input-number v-model.number="editPriceDetailForm.higH_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
            </el-col>

            <!-- 分类与关联 -->
            <el-col :span="6">
              <el-form-item label="住院收费类别">
                <el-select v-model="editPriceDetailForm.clasS_ON_INP_RCPT" placeholder="请选择住院收费类别"
                           style="width: 100%">
                  <el-option
                    v-for="item in inpRcptFeeDictList"
                    :key="item.feE_CLASS_CODE"
                    :label="item.feE_CLASS_NAME"
                    :value="item.feE_CLASS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="门诊收费类别">
                <el-select v-model="editPriceDetailForm.clasS_ON_OUTP_RCPT" placeholder="请选择门诊收费类别"
                           style="width: 100%">
                  <el-option
                    v-for="item in outpRcptFeeDictList"
                    :key="item.feE_CLASS_CODE"
                    :label="item.feE_CLASS_NAME"
                    :value="item.feE_CLASS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="核算项目类别">
                <el-select v-model="editPriceDetailForm.clasS_ON_RECKONING" placeholder="请选择核算项目类别"
                           style="width: 100%">
                  <el-option
                    v-for="item in reckItemClassDictList"
                    :key="item.clasS_CODE"
                    :label="item.clasS_NAME"
                    :value="item.clasS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="会计科目类别">
                <el-select v-model="editPriceDetailForm.subJ_CODE" placeholder="请选择会计科目类别" style="width: 100%">
                  <el-option
                    v-for="item in tallySubjecDictList"
                    :key="item.subJ_CODE"
                    :label="item.subJ_NAME"
                    :value="item.subJ_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 时间与状态 -->
            <el-col :span="6">
              <el-form-item label="开始日期">
                <el-date-picker
                  v-model="editPriceDetailForm.starT_DATE"
                  type="datetime"
                  placeholder="选择开始日期和时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="结束日期">
                <el-date-picker
                  v-model="editPriceDetailForm.stoP_DATE"
                  type="datetime"
                  placeholder="选择结束日期和时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="计费标记">
                <el-select v-model="editPriceDetailForm.chargE_FLAG" clearable placeholder="请选择" style="width: 100%">
                  <el-option label="不计费" value="0"/>
                  <el-option label="计费" value="1"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="计费范围">
                <el-select v-model="editPriceDetailForm.chargE_COPE" clearable placeholder="请选择" style="width: 100%">
                  <el-option label="仅门诊" value="0"/>
                  <el-option label="仅住院" value="1"/>
                  <el-option label="门诊住院均可" value="2"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="中医项目标识">
                <el-select v-model="editPriceDetailForm.tcM_ITEM_FLAG" clearable placeholder="请选择" style="width: 100%">
                  <el-option label="是" value="1"/>
                  <el-option label="否" value="0"/>
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 其他字段 -->
            <el-col :span="6">
              <el-form-item label="默认执行科室">
                <el-input v-model="editPriceDetailForm.performeD_BY"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="病案费用类别">
                <el-select v-model="editPriceDetailForm.clasS_ON_MR" placeholder="请选择病案费用类别" style="width: 100%">
                  <el-option
                    v-for="item in mrFeeClassDictList"
                    :key="item.mR_FEE_CLASS_CODE"
                    :label="item.mR_FEE_CLASS_NAME"
                    :value="item.mR_FEE_CLASS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="备注">
                <el-input v-model="editPriceDetailForm.memo" show-word-limit maxlength="200"/>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="物价代码">
                <el-input v-model="editPriceDetailForm.materiaL_CODE"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="输入码">
                <el-input v-model="editPriceDetailForm.inpuT_CODE" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="医保费用上传">
                <el-input v-model="editPriceDetailForm.iteM_CODE_MIU"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="项目内涵">
                <el-input v-model="editPriceDetailForm.connotation" type="textarea" :rows="2"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="除外内容">
                <el-input v-model="editPriceDetailForm.exclusions" type="textarea" :rows="2"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="说明">
                <el-input v-model="editPriceDetailForm.explain" type="textarea" :rows="2"/>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="医保国码">
                <el-input v-model="editPriceDetailForm.natioN_CODE"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="医保国码名称">
                <el-input v-model="editPriceDetailForm.natioN_NAME"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div style="text-align: right; margin-top: 15px;">
          <el-button type="warning" @click="updatePrice">修改价格</el-button>
          <el-button type="danger" @click="editPriceDetailDrawerVisible = false">取消</el-button>
          <el-button type="primary" @click="editSubmitPriceDetail">保存</el-button>
        </div>
      </div>
    </el-drawer>


    <!--  新增行价表详细信息-->
    <el-drawer
      title="新增价表详细信息"
      :visible.sync="AddPriceDetailDrawerVisible"
      direction="rtl"
      size="83%"
      :close-on-click-modal="false"
    >
      <div style="padding: 20px;">
        <el-form ref="addPriceDetailFormRef" :model="addPriceDetailForm" label-width="120px" label-position="right">
          <el-row :gutter="20">
            <!-- 基础信息 -->
            <el-col :span="8">
              <el-form-item label="项目类别">
                <el-select v-model="addPriceDetailForm.iteM_CLASS" placeholder="请选择项目类别">
                  <el-option
                    v-for="item in billItemClassDictList"
                    :key="item.classCode"
                    :label="item.className"
                    :value="item.classCode"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目代码">
                <el-input v-model="addPriceDetailForm.iteM_CODE"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目名称">
                <el-input v-model="addPriceDetailForm.iteM_NAME" @input="onNameInput" show-word-limit maxlength="50"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目规格">
                <el-input v-model="addPriceDetailForm.iteM_SPEC"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单位">
                <el-input v-model="addPriceDetailForm.units"/>
              </el-form-item>
            </el-col>

            <!-- 价格信息 -->
            <el-col :span="6">
              <el-form-item label="价格">
                <el-input-number v-model.number="addPriceDetailForm.price" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="优惠价格">
                <el-input-number v-model.number="addPriceDetailForm.prefeR_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="外宾价格">
                <el-input-number v-model.number="addPriceDetailForm.foreigneR_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="最高限价">
                <el-input-number v-model.number="addPriceDetailForm.higH_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
            </el-col>

            <!-- 分类与关联 -->
            <el-col :span="6">
              <el-form-item label="住院收费类别">
                <el-select v-model="addPriceDetailForm.clasS_ON_INP_RCPT" placeholder="请选择住院收费类别"
                           style="width: 100%">
                  <el-option
                    v-for="item in inpRcptFeeDictList"
                    :key="item.feE_CLASS_CODE"
                    :label="item.feE_CLASS_NAME"
                    :value="item.feE_CLASS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="门诊收费类别">
                <el-select v-model="addPriceDetailForm.clasS_ON_OUTP_RCPT" placeholder="请选择门诊收费类别"
                           style="width: 100%">
                  <el-option
                    v-for="item in outpRcptFeeDictList"
                    :key="item.feE_CLASS_CODE"
                    :label="item.feE_CLASS_NAME"
                    :value="item.feE_CLASS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="核算项目类别">
                <el-select v-model="addPriceDetailForm.clasS_ON_RECKONING" placeholder="请选择核算项目类别"
                           style="width: 100%">
                  <el-option
                    v-for="item in reckItemClassDictList"
                    :key="item.clasS_CODE"
                    :label="item.clasS_NAME"
                    :value="item.clasS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="会计科目类别">
                <el-select v-model="addPriceDetailForm.subJ_CODE" placeholder="请选择会计科目类别" style="width: 100%">
                  <el-option
                    v-for="item in tallySubjecDictList"
                    :key="item.subJ_CODE"
                    :label="item.subJ_NAME"
                    :value="item.subJ_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 时间与状态 -->
            <el-col :span="6">
              <el-form-item label="开始日期" prop="starT_DATE"
                            :rules="[{ required: true, message: '请选择开始日期和时间', trigger: 'change' }]">
                <el-date-picker
                  v-model="addPriceDetailForm.starT_DATE"
                  type="datetime"
                  placeholder="选择开始日期和时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="结束日期">
                <el-date-picker
                  v-model="addPriceDetailForm.stoP_DATE"
                  type="datetime"
                  placeholder="选择结束日期和时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="计费标记">
                <el-select v-model="addPriceDetailForm.chargE_FLAG" clearable placeholder="请选择" style="width: 100%">
                  <el-option label="不计费" value="0"/>
                  <el-option label="计费" value="1"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="计费范围">
                <el-select v-model="addPriceDetailForm.chargE_COPE" clearable placeholder="请选择" style="width: 100%">
                  <el-option label="仅门诊" value="0"/>
                  <el-option label="仅住院" value="1"/>
                  <el-option label="门诊住院均可" value="2"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="中医项目标识">
                <el-select v-model="addPriceDetailForm.tcM_ITEM_FLAG" clearable placeholder="请选择" style="width: 100%">
                  <el-option label="是" value="1"/>
                  <el-option label="否" value="0"/>
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 其他字段 -->
            <el-col :span="6">
              <el-form-item label="默认执行科室">
                <el-input v-model="addPriceDetailForm.performeD_BY"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="病案费用类别">
                <el-select v-model="addPriceDetailForm.clasS_ON_MR" placeholder="请选择病案费用类别" style="width: 100%">
                  <el-option
                    v-for="item in mrFeeClassDictList"
                    :key="item.mR_FEE_CLASS_CODE"
                    :label="item.mR_FEE_CLASS_NAME"
                    :value="item.mR_FEE_CLASS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="备注">
                <el-input v-model="addPriceDetailForm.memo" show-word-limit maxlength="200"/>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="物价代码">
                <el-input v-model="addPriceDetailForm.materiaL_CODE"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="输入码">
                <el-input v-model="addPriceDetailForm.inpuT_CODE" autocomplete="off" disabled/>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="医保费用上传">
                <el-input v-model="addPriceDetailForm.iteM_CODE_MIU"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="项目内涵">
                <el-input v-model="addPriceDetailForm.connotation" type="textarea" :rows="2"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="除外内容">
                <el-input v-model="addPriceDetailForm.exclusions" type="textarea" :rows="2"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="说明">
                <el-input v-model="addPriceDetailForm.explain" type="textarea" :rows="2"/>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="医保国码">
                <el-input v-model="addPriceDetailForm.natioN_CODE"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="医保国码名称">
                <el-input v-model="addPriceDetailForm.natioN_NAME"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div style="text-align: right; margin-top: 15px;">
          <el-button @click="AddPriceDetailDrawerVisible = false">取消</el-button>
          <el-button type="primary" @click="addSubmitPriceDetail">保存</el-button>
        </div>
      </div>
    </el-drawer>

    <!--  仅修改价钱详细信息-->
    <el-drawer
      title="修改价格详细信息"
      :visible.sync="updatePriceFormVisible"
      direction="rtl"
      size="83%"
      :close-on-click-modal="false"
    >
      <div style="padding: 20px;">
        <el-form ref="updatePriceDetailFormRef" :model="updatePriceForm" label-width="120px" label-position="right">
          <el-row :gutter="20">
            <!-- 基础信息 -->
            <el-col :span="8">
              <el-form-item label="项目类别">
                <el-select v-model="updatePriceForm.iteM_CLASS" placeholder="请选择项目类别" disabled>
                  <el-option
                    v-for="item in billItemClassDictList"
                    :key="item.classCode"
                    :label="item.className"
                    :value="item.classCode"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目代码">
                <el-input v-model="updatePriceForm.iteM_CODE" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目名称">
                <el-input v-model="updatePriceForm.iteM_NAME" @input="onNameInput" show-word-limit maxlength="50"
                          disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目规格">
                <el-input v-model="updatePriceForm.iteM_SPEC" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单位">
                <el-input v-model="updatePriceForm.units" disabled/>
              </el-form-item>
            </el-col>

            <!-- 价格信息 -->
            <el-col :span="6">
              <el-form-item label="价格">
                <el-input-number v-model.number="updatePriceForm.price" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="优惠价格">
                <el-input-number v-model.number="updatePriceForm.prefeR_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="外宾价格">
                <el-input-number v-model.number="updatePriceForm.foreigneR_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="最高限价">
                <el-input-number v-model.number="updatePriceForm.higH_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%" disabled/>
              </el-form-item>
            </el-col>

            <!-- 分类与关联 -->
            <el-col :span="6">
              <el-form-item label="住院收费类别">
                <el-select v-model="updatePriceForm.clasS_ON_INP_RCPT" placeholder="请选择住院收费类别"
                           style="width: 100%" disabled>
                  <el-option
                    v-for="item in inpRcptFeeDictList"
                    :key="item.feE_CLASS_CODE"
                    :label="item.feE_CLASS_NAME"
                    :value="item.feE_CLASS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="门诊收费类别">
                <el-select v-model="updatePriceForm.clasS_ON_OUTP_RCPT" placeholder="请选择门诊收费类别"
                           style="width: 100%" disabled>
                  <el-option
                    v-for="item in outpRcptFeeDictList"
                    :key="item.feE_CLASS_CODE"
                    :label="item.feE_CLASS_NAME"
                    :value="item.feE_CLASS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="核算项目类别">
                <el-select v-model="updatePriceForm.clasS_ON_RECKONING" placeholder="请选择核算项目类别"
                           style="width: 100%" disabled>
                  <el-option
                    v-for="item in reckItemClassDictList"
                    :key="item.clasS_CODE"
                    :label="item.clasS_NAME"
                    :value="item.clasS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="会计科目类别">
                <el-select v-model="updatePriceForm.subJ_CODE" placeholder="请选择会计科目类别" style="width: 100%" disabled>
                  <el-option
                    v-for="item in tallySubjecDictList"
                    :key="item.subJ_CODE"
                    :label="item.subJ_NAME"
                    :value="item.subJ_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 时间与状态 -->
            <el-col :span="6">
              <el-form-item label="开始日期" prop="starT_DATE"
                            :rules="[{ required: true, message: '请选择开始日期和时间', trigger: 'change' }]">
                <el-date-picker
                  v-model="updatePriceForm.starT_DATE"
                  type="datetime"
                  placeholder="选择开始日期和时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="结束日期">
                <el-date-picker
                  v-model="updatePriceForm.stoP_DATE"
                  type="datetime"
                  placeholder="选择结束日期和时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="计费标记">
                <el-select v-model="updatePriceForm.chargE_FLAG" clearable placeholder="请选择" style="width: 100%"
                           disabled>
                  <el-option label="不计费" value="0"/>
                  <el-option label="计费" value="1"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="计费范围">
                <el-select v-model="updatePriceForm.chargE_COPE" clearable placeholder="请选择" style="width: 100%"
                           disabled>
                  <el-option label="仅门诊" value="0"/>
                  <el-option label="仅住院" value="1"/>
                  <el-option label="门诊住院均可" value="2"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="中医项目标识">
                <el-select v-model="updatePriceForm.tcM_ITEM_FLAG" clearable placeholder="请选择" style="width: 100%"
                           disabled>
                  <el-option label="是" value="1"/>
                  <el-option label="否" value="0"/>
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 其他字段 -->
            <el-col :span="6">
              <el-form-item label="默认执行科室">
                <el-input v-model="updatePriceForm.performeD_BY" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="病案费用类别">
                <el-select v-model="updatePriceForm.clasS_ON_MR" placeholder="请选择病案费用类别" style="width: 100%" disabled>
                  <el-option
                    v-for="item in mrFeeClassDictList"
                    :key="item.mR_FEE_CLASS_CODE"
                    :label="item.mR_FEE_CLASS_NAME"
                    :value="item.mR_FEE_CLASS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="备注">
                <el-input v-model="updatePriceForm.memo" show-word-limit maxlength="200" disabled/>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="物价代码">
                <el-input v-model="updatePriceForm.materiaL_CODE" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="输入码">
                <el-input v-model="updatePriceForm.inpuT_CODE" autocomplete="off" disabled/>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="医保费用上传">
                <el-input v-model="updatePriceForm.iteM_CODE_MIU" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="项目内涵">
                <el-input v-model="updatePriceForm.connotation" type="textarea" :rows="2" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="除外内容">
                <el-input v-model="updatePriceForm.exclusions" type="textarea" :rows="2" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="说明">
                <el-input v-model="updatePriceForm.explain" type="textarea" :rows="2" disabled/>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="医保国码">
                <el-input v-model="updatePriceForm.natioN_CODE" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="医保国码名称">
                <el-input v-model="updatePriceForm.natioN_NAME" disabled/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div style="text-align: right; margin-top: 15px;">
          <el-button @click="updatePriceFormVisible = false">取消</el-button>
          <el-button type="primary" @click="updateSubmitPriceDetail">保存</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 价表名称维护弹窗 -->
    <el-dialog title="价表名称维护" :visible.sync="priceMaintenanceDialogVisible" width="88%"
               :close-on-click-modal="false">
      <el-form :model="priceMaintenanceForm" label-width="100px" label-position="right">
        <el-form-item label="项目名称">
          <el-input v-model="priceMaintenanceForm.iteM_NAME" disabled/>
        </el-form-item>
        <el-form-item label="项目代码">
          <el-input v-model="priceMaintenanceForm.iteM_CODE" disabled/>
        </el-form-item>

        <!-- 维护列表 -->
        <el-table :data="PriceItemNameList" stripe border class="main-table">
          <el-table-column label="项目类别" align="center">
            <template slot-scope="scope">
              <el-tag v-if="billItemClassDictList.find(item => item.classCode === scope.row.iteM_CLASS)"
                      type="info" effect="dark" size="mini">
                {{ billItemClassDictList.find(item => item.classCode === scope.row.iteM_CLASS).className }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column property="iteM_CODE" label="名称代码" align="center" show-overflow-tooltip/>
          <el-table-column property="iteM_NAME" label="名称" align="center" show-overflow-tooltip/>
          <el-table-column property="starT_DATE_TIME" label="开始时间" align="center" show-overflow-tooltip/>
          <el-table-column property="stoP_DATE_TIME" label="结束时间" align="center" show-overflow-tooltip/>
          <el-table-column label="操作" align="center" width="180">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" icon="el-icon-edit"
                         @click="openPriceNameDictDrawer(scope.row)">编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div style="margin-top: 10px; text-align: right">
          <el-button icon="el-icon-plus" @click="addPriceNameDictRow">新增行</el-button>
        </div>

      </el-form>
    </el-dialog>


    <!--    名称编辑弹窗-->
    <el-drawer
      title="价表名称编辑详细信息"
      :visible.sync="updatePriceNameDictByVisible"
      direction="btt"
      size="40%"
      :close-on-click-modal="false"
    >
      <div style="padding: 20px;">
        <el-form :model="updatePriceNameDictByForm" label-width="120px" label-position="right">
          <el-row :gutter="20">
            <!-- 基础信息 -->
            <el-col :span="6">
              <el-form-item label="项目代码">
                <el-input v-model="updatePriceNameDictByForm.iteM_CODE" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="项目名称">
                <el-input v-model="updatePriceNameDictByForm.iteM_NAME" @input="EditonNameInput" show-word-limit
                          maxlength="50" disabled/>
              </el-form-item>
            </el-col>
            <!-- 时间与状态 -->
            <el-col :span="6">
              <el-form-item label="开始日期">
                <el-date-picker
                  v-model="updatePriceNameDictByForm.starT_DATE_TIME"
                  type="datetime"
                  placeholder="选择开始日期和时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="输入码">
                <el-input v-model="updatePriceNameDictByForm.inpuT_CODE" disabled/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div style="text-align: right; margin-top: 15px;">
          <el-button type="danger" @click="updatePriceNameDictByVisible = false">取消</el-button>
          <el-button type="primary" @click="SavePriceNameDictBy">保存</el-button>
        </div>
      </div>
    </el-drawer>

    <!--    名称新增弹窗-->
    <el-drawer
      title="价表名称新增信息"
      :visible.sync="addPriceNameDictVisible"
      direction="btt"
      size="40%"
      :close-on-click-modal="false"
    >
      <div style="padding: 20px;">
        <el-form :model="addPriceNameDictForm" label-width="120px" label-position="right" ref="addPriceNameDictFormRef">
          <el-row :gutter="20">
            <!-- 基础信息 -->
            <el-col :span="6">
              <el-form-item label="项目分类" prop="iteM_CLASS">
                <el-select v-model="addPriceNameDictForm.iteM_CLASS" placeholder="请选择项目分类" style="width: 100%" disabled>
                  <el-option
                    v-for="item in billItemClassDictList"
                    :key="item.classCode"
                    :label="item.className"
                    :value="item.classCode"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="项目代码">
                <el-input v-model="addPriceNameDictForm.iteM_CODE" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目名称">
                <el-input v-model="addPriceNameDictForm.iteM_NAME" @input="AddonNameInput" show-word-limit
                          maxlength="50"/>
              </el-form-item>
            </el-col>
            <!-- 时间与状态 -->
            <el-col :span="6">
              <el-form-item label="开始日期" :rules="[{ required: true, message: '请选择开始日期和时间', trigger: 'change' }]">
                <el-date-picker
                  v-model="addPriceNameDictForm.starT_DATE_TIME"
                  type="datetime"
                  placeholder="选择开始日期和时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="输入码">
                <el-input v-model="addPriceNameDictForm.inpuT_CODE" disabled/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div style="text-align: right; margin-top: 15px;">
          <el-button type="danger" @click="addPriceNameDictVisible = false">取消</el-button>
          <el-button type="primary" @click="SaveAllPriceNameDict">保存</el-button>
        </div>
      </div>
    </el-drawer>


    <!-- 点击诊疗项目修改时间对话框 -->
    <el-dialog
      title="设置诊疗项目同步时间"
      :visible.sync="zhenLiaoDialogVisible"
      width="40%"
      :before-close="handleCloseZhenLiaoDialog">
      <el-form :model="zhenLiaoForm" label-width="100px">
        <el-form-item label="启用时间">
          <el-date-picker
            v-model="zhenLiaoForm.starT_DATE"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期时间"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="停用时间">
          <el-date-picker
            v-model="zhenLiaoForm.stoP_DATE"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期时间"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="zhenLiaoDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmZhenLiao">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 点击对照修改时间对话框 -->
    <el-dialog
      title="设置生产对照同步时间"
      :visible.sync="DuiZhaoDialogVisible"
      width="40%"
      :before-close="handleCloseDuiZhaoDialog">
      <el-form :model="DuiZhaoForm" label-width="100px">
        <el-form-item label="启用时间">
          <el-date-picker
            v-model="DuiZhaoForm.starT_DATE"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期时间"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="停用时间">
          <el-date-picker
            v-model="DuiZhaoForm.stoP_DATE"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期时间"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="DuiZhaoDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDuiZhao">确 定 同 步</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import {
  GetBillItemClassDict,
  GetClassTypeList, GetPriceItemNameDict,
} from "../../api/InspectionItems/PriceItem";
import {
  AddAllPriceList, AddBasePriceListToDict, AddDuiZhao, AddZhenLiaoProject,
  DeletePriceList, DeletePriceNameDictAll,
  EditPriceList,
  GetJiaBiaoList, SaveAllPriceNameDictSel,
  updateOnlyPrice, updatePriceNameDictByItemNameItemClass
} from "../../api/InspectionItems/JiaBiao";
import pinyin from "pinyin";

export default {
  name: 'JiaBiao',
  props: [],
  components: {},
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        inputCode: '',
        itemName: '',
      },
      billItemClassDictList: [],//账单项目分类
      inpRcptFeeDictList: [],// 住院收费
      mrFeeClassDictList: [],// 病案费用
      outpRcptFeeDictList: [],// 门诊收费
      reckItemClassDictList: [],//核算项目
      tallySubjecDictList: [],//会计科目,
      JiaBiaoList: [],//价表数据
      editPriceDetailForm: {},//编辑价表详情表单
      editPriceDetailDrawerVisible: false,

      stopDialogVisible: false, // 控制停用对话框显示
      stopForm: {
        stopDate: new Date().toISOString().slice(0, 19).replace('T', ' '), // 默认当前时间
        syncZhenLiao: false
      },
      currentRow: null, // 当前选中行数据
      currentRowStopNameDict: null, // 当前选中行数据
      AddPriceDetailDrawerVisible: false,
      addPriceDetailForm: {},//添加价表详情表单
      updatePriceForm: {},//只修改价表表单
      updatePriceFormVisible: false,//修改价表表单显示
      priceMaintenanceForm: {
        iteM_CLASS: "",
        iteM_CODE: "",
        starT_DATE: "",
        iteM_NAME: ""
      },
      PriceItemNameList: [],//项目名称
      // 项目名称维护相关
      priceMaintenanceDialogVisible: false,
      updatePriceNameDictByVisible: false,
      updatePriceNameDictByForm: {},//修改项目名称
      tempUpdatePriceNameDictByItemName: "",
      tempUpdatePriceNameDictByItemClass: "",
      tempUpdatePriceNameDictByStartDate: "",
      addPriceNameDictVisible: false,
      addPriceNameDictForm: {},
      zhenLiaoDialogVisible: false,//诊疗维护
      zhenLiaoForm: {},//诊疗项目
      DuiZhaoForm: {},//对照表
      DuiZhaoDialogVisible: false,//对照维护
    }
  },
  created() {
    this.getDict()
  },
  mounted() {
  },
  methods: {
    getDict() {
      GetBillItemClassDict().then(res => {
        this.billItemClassDictList = res.data
        this.getClassType()
      })
    },
    getClassType() {
      GetClassTypeList().then(res => {
        this.inpRcptFeeDictList = res.data.inpRcptFeeDictRows
        this.mrFeeClassDictList = res.data.mrFeeClassDictRows
        this.outpRcptFeeDictList = res.data.outpRcptFeeDictRows
        this.reckItemClassDictList = res.data.reckItemClassDictRows
        this.tallySubjecDictList = res.data.tallySubjecDictRows
        this.getList()
      })
    },
    getList() {
      GetJiaBiaoList(this.queryParams).then(res => {
        this.JiaBiaoList = res.data.rows
        this.queryParams.total = res.data.total
      })
    },
    // 查询
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 打开抽屉并加载当前行数据
    openPriceDetailDrawer(row) {
      this.editPriceDetailForm = {...row}; // 复制当前行数据到抽屉表单
      this.editPriceDetailForm.tempEditItemClass = row.iteM_CLASS
      this.editPriceDetailForm.tempEditItemCode = row.iteM_CODE
      this.editPriceDetailForm.tempEditItemSpec = row.iteM_SPEC
      this.editPriceDetailForm.tempEditUnits = row.units
      this.editPriceDetailForm.tempEditStartDate = row.starT_DATE
      this.editPriceDetailDrawerVisible = true;
    },

    // 抽屉编辑提交 数据
    editSubmitPriceDetail() {
      //保存到数据库
      EditPriceList(this.editPriceDetailForm).then(() => {
        this.getList(); // 刷新主列表以显示最新数据
      }).catch(error => {
        console.error('数据更新失败:', error);
        this.$message.error('数据更新失败');
      });
      this.editPriceDetailDrawerVisible = false;
    },

    // 打开停用对话框
    openStopDialog(row) {
      this.currentRow = row;
      const now = new Date();
      const timezoneOffset = now.getTimezoneOffset() * 60000; // 获取时区偏移（毫秒）
      const localNow = new Date(now - timezoneOffset); // 调整为本地时间
      this.stopForm.stopDate = localNow.toISOString().slice(0, 19).replace('T', ' '); // 设置默认时间为当前时间
      this.stopDialogVisible = true;
    },

    // 关闭停用对话框
    handleCloseStopDialog(done) {
      this.stopDialogVisible = false;
      done();
    },


    // 确认停用操作
    confirmStop() {
      if (!this.currentRow) {
        this.$message.error("没有选中的数据");
        return;
      }
      const temp = {
        iteM_CLASS: this.currentRow.iteM_CLASS,
        iteM_CODE: this.currentRow.iteM_CODE,
        iteM_SPEC: this.currentRow.iteM_SPEC,
        iteM_NAME: this.currentRow.iteM_NAME,
        units: this.currentRow.units,
        starT_DATE: this.currentRow.starT_DATE,
        stoP_DATE: this.stopForm.stopDate, // 添加停用时间
        syncZhenLiao: this.stopForm.syncZhenLiao,
      };

      this.$confirm('是否确认停用该数据项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DeletePriceList(temp).then(() => {
          this.$message.success('停用成功');
          this.getList();
        }).catch(error => {
          console.error('停用失败：', error);
          this.$message.error('停用失败');
        });
      }).catch(() => {
        this.$message.info('已取消停用');
      });
      this.stopDialogVisible = false;
    },

    //新增行
    addPriceRow() {
      this.addPriceDetailForm = {
        iteM_CODE: "",
        iteM_NAME: "",
        iteM_CLASS: "",
        starT_DATE: "",
        units: "",
        iteM_SPEC: "",
        price: 0.00,
        prefeR_PRICE: 0.00,
        foreigneR_PRICE: 0.00,
        higH_PRICE: 0.00,
        clasS_ON_INP_RCPT: "",
        clasS_ON_OUTP_RCPT: "",
        clasS_ON_RECKONING: "",
        subJ_CODE: "",
        stoP_DATE: "",
        chargE_FLAG: "",
        chargE_COPE: "",
        tcM_ITEM_FLAG: "",
        performeD_BY: "",
        feE_TYPE_MASK: "",
        clasS_ON_MR: "",
        memo: "",
        materiaL_CODE: "",
        inpuT_CODE: "",
        clasS_ON_MR_NEW: "",
        clasS_ON_MR_OLD: "",
        iteM_CODE_MIU: "",
        connotation: "",
        exclusions: "",
        explain: "",
        natioN_CODE: "",
        natioN_NAME: ""
      };
      this.AddPriceDetailDrawerVisible = true;
    },

    // 项目名称输入自动生成输入码
    onNameInput(val) {
      if (val) {
        // 取首字母大写输入码
        const pyArr = pinyin(val, {style: pinyin.STYLE_FIRST_LETTER})
        this.addPriceDetailForm.inpuT_CODE = pyArr.map(arr => arr[0]).join('').toUpperCase()
      } else {
        this.addPriceDetailForm.inpuT_CODE = ''
      }
    },


    EditonNameInput(val) {
      if (val) {
        // 取首字母大写输入码
        const pyArr = pinyin(val, {style: pinyin.STYLE_FIRST_LETTER})
        this.updatePriceNameDictByForm.inpuT_CODE = pyArr.map(arr => arr[0]).join('').toUpperCase()
      } else {
        this.updatePriceNameDictByForm.inpuT_CODE = ''
      }
    },

    AddonNameInput(val) {
      if (val) {
        // 取首字母大写输入码
        const pyArr = pinyin(val, {style: pinyin.STYLE_FIRST_LETTER})
        this.addPriceNameDictForm.inpuT_CODE = pyArr.map(arr => arr[0]).join('').toUpperCase()
      } else {
        this.addPriceNameDictForm.inpuT_CODE = ''
      }
    },


    // 新增行
    addSubmitPriceDetail() {
      this.$refs.addPriceDetailFormRef.validate(valid => {
        if (valid) {
          //保存到数据库
          AddAllPriceList(this.addPriceDetailForm).then(() => {
            //提示信息
            this.$message.success('数据保存成功');
            this.getList(); // 刷新主列表以显示最新数据
          }).catch(error => {
            console.error('数据更新失败:', error);
            this.$message.error('数据更新失败');
          });
          this.AddPriceDetailDrawerVisible = false;
        } else {
          this.$message.error('请完善表单信息');
          return false;
        }
      });

    },

    //修改价格
    updatePrice() {
      this.updatePriceForm = {
        iteM_CODE: this.editPriceDetailForm.iteM_CODE,
        iteM_NAME: this.editPriceDetailForm.iteM_NAME,
        iteM_CLASS: this.editPriceDetailForm.iteM_CLASS,
        starT_DATE: "",
        units: this.editPriceDetailForm.units,
        iteM_SPEC: this.editPriceDetailForm.iteM_SPEC,
        price: 0.00,
        prefeR_PRICE: 0.00,
        foreigneR_PRICE: 0.00,
        higH_PRICE: this.editPriceDetailForm.higH_PRICE,
        clasS_ON_INP_RCPT: this.editPriceDetailForm.clasS_ON_INP_RCPT,
        clasS_ON_OUTP_RCPT: this.editPriceDetailForm.clasS_ON_OUTP_RCPT,
        clasS_ON_RECKONING: this.editPriceDetailForm.clasS_ON_RECKONING,
        subJ_CODE: this.editPriceDetailForm.subJ_CODE,
        stoP_DATE: "",
        chargE_FLAG: this.editPriceDetailForm.chargE_FLAG,
        chargE_COPE: this.editPriceDetailForm.chargE_COPE,
        tcM_ITEM_FLAG: this.editPriceDetailForm.tcM_ITEM_FLAG,
        performeD_BY: this.editPriceDetailForm.performeD_BY,
        feE_TYPE_MASK: this.editPriceDetailForm.feE_TYPE_MASK,
        clasS_ON_MR: this.editPriceDetailForm.clasS_ON_MR,
        memo: this.editPriceDetailForm.memo,
        materiaL_CODE: this.editPriceDetailForm.materiaL_CODE,
        inpuT_CODE: this.editPriceDetailForm.inpuT_CODE,
        clasS_ON_MR_NEW: this.editPriceDetailForm.clasS_ON_MR_NEW,
        clasS_ON_MR_OLD: this.editPriceDetailForm.clasS_ON_MR_OLD,
        iteM_CODE_MIU: this.editPriceDetailForm.iteM_CODE_MIU,
        connotation: this.editPriceDetailForm.connotation,
        exclusions: this.editPriceDetailForm.exclusions,
        explain: this.editPriceDetailForm.explain,
        natioN_CODE: this.editPriceDetailForm.natioN_CODE,
        natioN_NAME: this.editPriceDetailForm.natioN_NAME
      };
      this.editPriceDetailDrawerVisible = false;
      this.updatePriceFormVisible = true;
    },

// 修改行
    updateSubmitPriceDetail() {
      this.$refs.updatePriceDetailFormRef.validate(valid => {
        if (valid) {
          //保存到数据库
          updateOnlyPrice(this.updatePriceForm).then(() => {
            this.$message.success('价钱保存成功');
            this.getList(); // 刷新主列表以显示最新数据
          }).catch(error => {
            console.error('数据更新失败:', error);
            this.$message.error('数据更新失败');
          });
          this.AddPriceDetailDrawerVisible = false;
          this.updatePriceFormVisible = false
        } else {
          this.$message.error('请完善表单信息');
          return false;
        }
      });
    },

    // 打开价格维护
    openPriceMaintenance(row) {
      this.priceMaintenanceForm = {
        iteM_CLASS: row.iteM_CLASS,
        iteM_CODE: row.iteM_CODE,
        starT_DATE: row.starT_DATE,
        iteM_NAME: row.iteM_NAME
      };


      const query = {
        itemClass: row.iteM_CLASS,
        itemCode: row.iteM_CODE,
        startDate: row.starT_DATE,
      };

      GetPriceItemNameDict(query).then(res => {
        this.PriceItemNameList = res.data.rows
      }).catch(error => {
        console.error('数据更新失败:', error);
        this.$message.error('数据更新失败');
      });
      this.priceMaintenanceDialogVisible = true;
    },
    openPriceNameDictDrawer(row) {
      this.updatePriceNameDictByForm = {...row}; // 复制当前行数据到抽屉表单
      this.tempUpdatePriceNameDictByItemName = row.iteM_NAME
      this.tempUpdatePriceNameDictByItemClass = row.iteM_CLASS
      this.tempUpdatePriceNameDictByStartDate = row.starT_DATE_TIME
      this.updatePriceNameDictByVisible = true;
    },

    // 保存名称字典
    SavePriceNameDictBy() {
      this.updatePriceNameDictByForm.tempUpdatePriceNameDictByItemName = this.tempUpdatePriceNameDictByItemName
      this.updatePriceNameDictByForm.tempUpdatePriceNameDictByItemClass = this.tempUpdatePriceNameDictByItemClass
      this.updatePriceNameDictByForm.tempUpdatePriceNameDictByStartDate = this.tempUpdatePriceNameDictByStartDate
      updatePriceNameDictByItemNameItemClass(this.updatePriceNameDictByForm).then(res => {
        const query = {
          itemClass: this.priceMaintenanceForm.iteM_CLASS,
          itemCode: this.priceMaintenanceForm.iteM_CODE,
          startDate: this.priceMaintenanceForm.starT_DATE,
        };
        GetPriceItemNameDict(query).then(res => {
          this.PriceItemNameList = res.data.rows
        })
        this.getList();
        this.updatePriceNameDictByVisible = false;
      })
    },
    addPriceNameDictRow() {
      this.addPriceNameDictForm = {
        iteM_CODE: this.priceMaintenanceForm.iteM_CODE,
        iteM_CLASS: this.priceMaintenanceForm.iteM_CLASS,
        iteM_NAME: "",
        inpuT_CODE: "",
        starT_DATE_TIME: "",
      }
      this.addPriceNameDictVisible = true;
    },

    SaveAllPriceNameDict() {
      this.$refs.addPriceNameDictFormRef.validate(valid => {
        if (valid) {
          //保存到数据库
          SaveAllPriceNameDictSel(this.addPriceNameDictForm).then(() => {
            const query = {
              itemClass: this.priceMaintenanceForm.iteM_CLASS,
              itemCode: this.priceMaintenanceForm.iteM_CODE,
              startDate: this.priceMaintenanceForm.starT_DATE,
            };
            GetPriceItemNameDict(query).then(res => {
              this.PriceItemNameList = res.data.rows
            })
            this.getList(); // 刷新主列表以显示最新数据
          }).catch(error => {
            console.error('数据更新失败:', error);
            this.$message.error('数据更新失败');
          });
          this.addPriceNameDictVisible = false;
          this.priceMaintenanceDialogVisible = true
        } else {
          this.$message.error('请完善表单信息');
          return false;
        }
      });
    },

    //生成诊疗项目
    GetZhenLiaoProject(row) {
      console.log(1, row)
      this.zhenLiaoForm = row;
      this.zhenLiaoForm.tempStartDate = row.starT_DATE
      this.zhenLiaoDialogVisible = true;
    },

    handleCloseZhenLiaoDialog(done) {
      this.zhenLiaoDialogVisible = false;
      done()
    },

    confirmZhenLiao() {
      AddZhenLiaoProject(this.zhenLiaoForm).then(() => {
        this.$message.success('生成成功')
        this.getList()
      }).catch(error => {
        console.error('生成失败:', error);
        this.$message.error('生成失败');
      });
      this.zhenLiaoDialogVisible = false;
    },

    // 生成对照
    GetDuiZhao(row) {
      this.DuiZhaoForm = row;
      this.DuiZhaoForm.tempStartDate = row.starT_DATE
      this.DuiZhaoDialogVisible = true;
    },

    handleCloseDuiZhaoDialog(done) {
      this.DuiZhaoDialogVisible = false;
      done()
    },
    confirmDuiZhao() {
      AddDuiZhao(this.DuiZhaoForm).then(() => {
        this.$message.success('对照成功')
        this.getList()
      }).catch(error => {
        console.error('对照失败:', error);
        this.$message.error('对照失败');
      });
      this.DuiZhaoDialogVisible = false;
    }


  }
  ,
}
</script>

<style scoped>

</style>
