<template>
  <div class="container">
    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryForm" size="mini" :inline="true" class="filter-form"
             style="margin-bottom: 15px;">
      <el-form-item label="检查分类" label-width="80px">
        <el-select v-model="queryParams.examClassName" placeholder="请选择检查分类" clearable
                   @change="handleQuery" style="width: 200px">
          <el-option
            v-for="item in examClassNameList"
            :key="item.exaM_CLASS_NAME"
            :label="item.exaM_CLASS_NAME"
            :value="item.exaM_CLASS_NAME"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button-group>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button type="success" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        </el-button-group>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table :data="ExamSubclassDictList" stripe border class="main-table">
      <el-table-column property="seriaL_NO" label="序号" align="center" width="80"></el-table-column>
      <el-table-column property="exaM_CLASS_NAME" label="检查分类" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column property="exaM_SUBCLASS_NAME" label="检查子类" align="center"
                       show-overflow-tooltip></el-table-column>
      <el-table-column property="inpuT_CODE" label="输入码" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" align="center" width="120">
        <template slot-scope="scope">
          <el-button size="small" type="primary" icon="el-icon-edit" @click="handleEdit(scope.row)"
                     circle plain></el-button>
          <!-- <el-button size="small" type="danger" icon="el-icon-delete" @click="handleDelete(scope.row)"
                       circle plain></el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination v-show="queryParams.total > 0"
                :total="queryParams.total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 新增对话框 -->
    <el-dialog title="新增检查子类" :visible.sync="addDialogVisible" width="40%" center>
      <el-form :model="addForm" ref="addForm" :rules="rules" label-width="100px" class="dialog-form">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="检查分类" prop="exaM_CLASS_NAME">
              <el-select v-model="addForm.exaM_CLASS_NAME" placeholder="请选择检查分类" style="width: 100%">
                <el-option
                  v-for="item in examClassNameList"
                  :key="item.exaM_CLASS_NAME"
                  :label="item.exaM_CLASS_NAME"
                  :value="item.exaM_CLASS_NAME"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="检查子类" prop="exaM_SUBCLASS_NAME">
              <el-input v-model="addForm.exaM_SUBCLASS_NAME" autocomplete="off" @input="onNameInput"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="输入码" prop="inpuT_CODE">
              <el-input v-model="addForm.inpuT_CODE" autocomplete="off" placeholder="自动生成输入码" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item class="form-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAddForm">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 编辑对话框 -->
    <el-dialog title="编辑检查子类" :visible.sync="editDialogVisible" width="40%" center>
      <el-form :model="editForm" ref="editForm" :rules="rules" label-width="100px" class="dialog-form">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="检查分类" prop="exaM_CLASS_NAME">
              <el-select v-model="editForm.exaM_CLASS_NAME" placeholder="请选择检查分类" style="width: 100%">
                <el-option
                  v-for="item in examClassNameList"
                  :key="item.exaM_CLASS_NAME"
                  :label="item.exaM_CLASS_NAME"
                  :value="item.exaM_CLASS_NAME"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="检查子类" prop="exaM_SUBCLASS_NAME">
              <el-input v-model="editForm.exaM_SUBCLASS_NAME" autocomplete="off" @input="editOnNameInput"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="输入码" prop="inpuT_CODE">
              <el-input v-model="editForm.inpuT_CODE" autocomplete="off" placeholder="自动生成输入码" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item class="form-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEditForm">更新</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  GetExamClassNameInfo,
  GetExamSubclassDictInfo,
  SaveExamSubclassDict,
  UpdateExamSubclassDict
} from "../../api/InspectionItems/ExamSubclassDict";
import pinyin from "pinyin";

export default {
  name: 'ExamSubclassDict',
  props: [],
  components: {},
  data() {
    return {
      // 查询参数
      queryParams: {
        examClassName: "",
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      // 列表数据
      ExamSubclassDictList: [],
      // 检查分类
      examClassNameList: [],
      // 控制对话框显示
      addDialogVisible: false,
      editDialogVisible: false,
      // 表单数据
      addForm: {
        exaM_CLASS_NAME: "",
        exaM_SUBCLASS_NAME: "",
        inpuT_CODE: ""
      },
      editForm: {},
      // 表单验证规则
      rules: {
        exaM_CLASS_NAME: [
          {required: true, message: "请选择检查分类", trigger: "change"}
        ],
        exaM_SUBCLASS_NAME: [
          {required: true, message: "请输入检查子类", trigger: "blur"},
          {min: 2, max: 50, message: "长度在2到50个字符", trigger: "blur"}
        ],
        inpuT_CODE: [
          {required: true, message: "请输入输入码", trigger: "blur"},
          {min: 1, max: 20, message: "长度在1到20个字符", trigger: "blur"}
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getDict()
  },
  methods: {
    getList() {
      GetExamSubclassDictInfo(this.queryParams).then(res => {
        this.ExamSubclassDictList = res.data.rows
        this.queryParams.total = res.data.total
      })
    },
    handleQuery() {
      this.getList();
    },
    getDict() {
      GetExamClassNameInfo().then(res => {
        this.examClassNameList = res.data
      })
    },
    handleAdd() {
      this.addDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.addForm.resetFields();
      });
    },
    submitAddForm() {
      this.$refs.addForm.validate(valid => {
        if (valid) {
          SaveExamSubclassDict(this.addForm).then(res => {
            this.addDialogVisible = false;
            this.getList(); // 刷新列表
            this.$message.success('新增成功');
          }).catch(error => {
            console.error('保存失败', error);
            this.$message.error('新增失败，请重试');
          });
        } else {
          return false;
        }
      });
    },

    // 新增项目名称输入自动生成输入码
    onNameInput(val) {
      if (val) {
        // 取首字母大写输入码
        const pyArr = pinyin(val, {style: pinyin.STYLE_FIRST_LETTER})
        this.addForm.inpuT_CODE = pyArr.map(arr => arr[0]).join('').toUpperCase()
      } else {
        this.addForm.inpuT_CODE = ''
      }
    },


    handleEdit(row) {
      this.editForm = {...row};
      this.editDialogVisible = true;
    },
    submitEditForm() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          UpdateExamSubclassDict(this.editForm).then(res => {
            this.editDialogVisible = false;
            this.getList();
            this.$message.success('更新成功');
          }).catch(error => {
            console.error('更新失败', error);
            this.$message.error('更新失败，请重试');
          });
        } else {
          return false;
        }
      });
    },
    // 修改项目名称输入自动生成输入码
    editOnNameInput(val) {
      if (val) {
        // 取首字母大写输入码
        const pyArr = pinyin(val, {style: pinyin.STYLE_FIRST_LETTER})
        this.editForm.inpuT_CODE = pyArr.map(arr => arr[0]).join('').toUpperCase()
      } else {
        this.editForm.inpuT_CODE = ''
      }
    },
  },
}
</script>

<style scoped>
.container {
  padding: 20px;
}

.filter-form {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.main-table {
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
}

.dialog-form {
  padding: 10px;
}

.form-footer {
  text-align: right;
}

.el-button-group .el-button:not(:first-child):not(:last-child) {
  border-radius: 4px;
}

.el-button-group .el-button:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.el-button-group .el-button:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
</style>
