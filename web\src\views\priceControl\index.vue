<template>
  <div class="single-master">
    <div class="single-title">价格维护</div>
    <div class="single-element">
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <el-form-item label="项目分类:">
              <el-select @change="itemClassChange" clearable v-model="queueForm.itemClass" placeholder="查询时请先选择">
                <el-option
                  v-for="item in dataDict.billItemDict" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="项目名称:">
              <div class="mySelect">
                <el-select clearable v-model="queueForm.itemCode" placeholder="选择分类后通过简拼/名称检索"
                           @change="itemCodeCharge" :filter-method="filterOne" filterable>
                  <el-option
                    v-for="item in priceListDictNew" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </div>

            </el-form-item>
            <el-form-item label="项目编码:">
              <el-input @input="codeInput" clearable v-model="queueForm.itemCodeVague" placeholder="支持模糊查询"/>
            </el-form-item>
            <el-form-item label="状态:">
              <el-select clearable v-model="queueForm.type" @change="getPricePageList" placeholder="请选择">
                <el-option
                  v-for="item in statusDict" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="getPricePageList">查询</el-button>
              <el-button type="primary" icon="el-icon-refresh" @click="selectReset">重置查询</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="my-button-function">
          <div class="button-item">
            <el-button type="success" icon="el-icon-plus" @click="addProject">新增</el-button>
          </div>
          <div class="button-item">
            <el-button v-if="rowTable && queueForm.type === '1'" type="success" icon="el-icon-tickets" @click="projectName">项目名称</el-button>
            <el-button v-else type="success" disabled icon="el-icon-tickets" @click="projectName">项目名称</el-button>
          </div>
          <div class="button-item">
            <el-button v-if="rowTable && queueForm.type === '1'" type="success" icon="el-icon-tickets" @click="contrastList">对照列表</el-button>
            <el-button v-else type="success" disabled icon="el-icon-tickets" @click="contrastList">对照列表</el-button>
          </div>
          <div class="button-item">
            <el-button v-if="rowTable" type="success" icon="el-icon-tickets" @click="pastRecords">历史价格</el-button>
            <el-button v-else type="success" disabled icon="el-icon-tickets" @click="pastRecords">历史价格</el-button>
          </div>
          <div class="button-item">
            <el-button v-if="rowTable" type="warning" icon="el-icon-edit-outline" @click="updateProject">修改
            </el-button>
            <el-button v-else disabled type="warning" icon="el-icon-edit-outline" @click="updateProject">修改
            </el-button>
          </div>
          <div class="button-item">
            <el-button v-if="rowTable" type="danger" icon="el-icon-close" @click="stopProject('1')">停用</el-button>
            <el-button v-else disabled type="danger" icon="el-icon-close" @click="stopProject('1')">停用</el-button>
          </div>
          <div class="button-item">
            <el-button v-if="rowTable && queueForm.type === '2'" type="danger" icon="el-icon-check" @click="startProject">启用</el-button>
            <el-button v-else disabled type="danger" icon="el-icon-check" @click="startProject">启用</el-button>
          </div>
          <div class="button-item">
            <el-button v-if="selectionTable" type="danger" icon="el-icon-close" @click="stopProject('2')">批量停用</el-button>
            <el-button v-else disabled type="danger" icon="el-icon-close" @click="stopProject('2')">批量停用</el-button>
          </div>
          <div class="button-item">
            <el-button v-if="selectionTable && queueForm.type === '2'" type="danger" icon="el-icon-check" @click="startProject">批量启用</el-button>
            <el-button v-else disabled type="danger" icon="el-icon-check" @click="startProject">批量启用</el-button>
          </div>
<!--          <div class="button-item">-->
<!--            <el-button type="success" icon="el-icon-download" @click="downloadProject">导入模板下载</el-button>-->
<!--          </div>-->
<!--          <div class="button-item">-->
<!--            <el-button type="success" icon="el-icon-upload2" @click="importProject">导入</el-button>-->
<!--          </div>-->
        </div>
        <div class="element-table">
          <el-table :data="tableData" style="width: 100%" border :height="(tableHeight-380)" highlight-current-row
                    @row-click="myTableRowClick" @row-dblclick="myTableRowDoubleClick"
                    @select="handleSelectionChange" @select-all="handleSelectionChange">
            <el-table-column type="selection" align="center" width="55"></el-table-column>
            <el-table-column type="index" width="40" align="center"></el-table-column>
            <el-table-column property="itemLabel" label="项目类别" width="80" align="center" show-overflow-tooltip>
            </el-table-column>
            <el-table-column property="iteM_CODE" label="项目代码" align="center" show-overflow-tooltip/>
            <el-table-column property="iteM_NAME" label="项目名称" align="center" show-overflow-tooltip/>
            <el-table-column property="iteM_SPEC" label="项目规格" align="center" show-overflow-tooltip/>
            <el-table-column property="units" label="单位" align="center" show-overflow-tooltip/>
            <el-table-column property="price" label="价格" width="100" align="center" show-overflow-tooltip/>
            <el-table-column property="starT_DATE" label="开始日期" align="center" show-overflow-tooltip/>
            <el-table-column property="stoP_DATE" label="停用日期" align="center" show-overflow-tooltip/>
          </el-table>
        </div>
      </div>
      <div class="element-page">
        <pagination v-show="total > 0"
                    :total="total"
                    :page.sync="queueForm.pageNum"
                    :limit.sync="queueForm.pageSize"
                    @pagination="getPricePageList"
                    :page-sizes="[10, 50, 100,200,500]"
                    :key="pageKey"/>
      </div>
      <insert-form ref="insertFormRefs" :data-dict="dataDict" @save-success="getPricePageList"></insert-form>
      <update-form ref="updateFormRefs" :data-dict="dataDict" @update-refresh="getPricePageList"></update-form>
      <stop-date ref="stopDateRefs" @stop-success="getPricePageList"></stop-date>
      <price-project-name ref="priceProjectNameRefs" :tableHeight="tableHeight"></price-project-name>
      <charge-vs-project ref="chargeVsProjectRefs" :tableHeight="tableHeight"></charge-vs-project>
    </div>
  </div>
</template>

<script>
import {GetPricePageList, GetPriceCorrelationDict, GetPriceListTree} from '@/api/diagnosis/priceControl'
import InsertForm from "./model/insertForm.vue";
import UpdateForm from "./model/updateForm.vue";
import StopDate from "./model/stopDate.vue";
import PriceProjectName from "./model/priceProjectName.vue";
import ChargeVsProject from "./model/chargeVsProject.vue";

export default {
  name: 'index',
  props: [],
  components: {ChargeVsProject, PriceProjectName, StopDate, UpdateForm, InsertForm},
  data() {
    return {
      queueForm: {
        type: '1',
        pageNum: 1,
        pageSize: 10,
        itemClass: '',
        itemCode: '',
        itemCodeVague: '',
      },
      dataDict: {},
      statusDict: [
        {value: '1', label: '未停用'},
        {value: '2', label: '已停用'},
      ],
      tableHeight: undefined,
      tableData: [],
      total: 0,
      pageKey: 0,
      priceListDict: [],
      priceListDictNew: [],
      rowTable: undefined,
      selectionTable: undefined,
    }
  },
  created() {
    this.handleResize()
    this.getPricePageList();
    this.getPriceCorrelationDict();
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    pastRecords(){
      this.thisMsgbox();
    },
    projectName(){
      this.$refs.priceProjectNameRefs.init(this.rowTable);
    },
    contrastList(){
      this.$refs.chargeVsProjectRefs.init(this.rowTable);
    },
    startProject(){
      //需要重新增加数据
      this.thisMsgbox();
    },
    stopProject(type) {
      let data = [];
      if (type === '1'){
        data.push(this.rowTable)
        this.$refs.stopDateRefs.init(data,type);
      }else if (type === '2'){
        data = this.selectionTable
        this.$refs.stopDateRefs.init(data,type);
      }
    },
    updateProject() {
      this.$refs.updateFormRefs.init(this.rowTable);
    },
    downloadProject(){
      this.thisMsgbox();
    },
    importProject(){
      this.thisMsgbox();
    },
    addProject() {
      this.$refs.insertFormRefs.init(null, "1");
    },
    handleSelectionChange(val) {
      this.selectionTable = val;
    },
    myTableRowClick(row, column, event) {
      this.rowTable = row;
    },
    myTableRowDoubleClick(row, column, event) {
    },
    itemCodeCharge() {
      this.queueForm.itemCodeVague = "";
      this.priceListDictNew = [];
    },
    codeInput() {
      this.queueForm.itemCode = "";
    },
    filterOne(query) {
      this.queueForm.itemCode = query;
      if (query !== "" || query) {
        this.priceListDictNew = this.priceListDict.filter((item) => {
          if (item.label.toString().indexOf(query) > -1 ||
            item.inputCode.toUpperCase().toString().indexOf(query.toUpperCase()) > -1) {
            return true;
          }
        })
      } else {
        this.priceListDictNew = [];
      }
    },
    itemClassChange(data) {
      this.priceListDict = [];
      const loading = this.$loading({
        lock: true,
        text: '正在努力获取字典中,请耐心等待!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      GetPriceListTree(data).then(res => {
        if (res.code === 200) {
          this.priceListDict = res.data;
        }
      }).finally(() => {
        loading.close();
      })
    },
    handleResize() {
      this.tableHeight = window.innerHeight // 更新高度数据
    },
    getPricePageList() {
      ++this.pageKey;
      this.total = 0;
      this.tableData = [];
      this.rowTable = undefined;
      this.selectionTable = undefined;
      const loading = this.$loading({
        lock: true,
        text: '数据正在努力提取中,请耐心等待!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      GetPricePageList(this.queueForm).then(res => {
        if (res.code === 200) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        }
      }).finally(() => {
        loading.close();
      })
    },
    getPriceCorrelationDict() {
      GetPriceCorrelationDict().then(response => {
        if (response.code === 200) {
          this.dataDict = response.data;
        }
      })
    },
    selectReset() {
      this.queueForm = {
        type: this.queueForm.type,
        pageNum: 1,
        pageSize: 10,
        itemClass: '',
        itemCode: '',
        itemCodeVague: '',
      };
    },
    thisMsgbox() {
      this.$msgbox.alert(
        '<div style="font-size: 28px !important;color: red; text-align: center;font-weight: 800;margin-bottom: 10px;">' +
        '功能开发中' + '</div>',
        '系统提示',
        {
          confirmButtonText: '确定',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }).then(() => {
      })
    },
  },
}
</script>

<style scoped lang="scss">
@import "../../assets/styles/singlePage";

.mySelect {
  ::v-deep.el-date-editor.el-input, .el-date-editor.el-input__inner {
    width: 260px !important;
  }

  ::v-deep.el-input--medium .el-input__inner {
    width: 260px !important;
    height: 31px;
    line-height: 36px;
  }
}

.my-button-function {
  margin-bottom: 5px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  .button-item{
    margin-left: 2px !important;
    margin-top: 1px;
  }
}
</style>
