<template>
  <!-- 诊疗项目字典维护 -->
  <div class="inherit-platform">
    <div class="page-header">
      <div class="left">
        <div class="title">检查项目维护</div>
        <el-row :gutter="10" type="flex" align="middle">
          <el-select v-model="queryParams.ifend" placeholder="停用时间过滤" @change="getList"
            style="width: 200px; margin-left: 10px;">
            <el-option v-for="(item, index) in filterIfend" :key="index" :label="item.label" :value="item.value" />
          </el-select>
          <el-input v-model="searchKeyword" placeholder="请输入项目名称" prefix-icon="el-icon-search" @input="handleSearch"
            clearable style="flex: 1;"></el-input>
  
        </el-row>
      </div>
      <div class="right">
        <el-row :gutter="10">
          <el-button type="primary" @click="getList()">刷新</el-button>
          <el-button type="primary" @click="toAdd()">新增</el-button>
        </el-row>
      </div>
    </div>
  
    <el-table v-loading="loading" :data="dateList" border height="600" stripe highlight-current-row style="width: 100%">
      <el-table-column type="index" label="序号" align="center" />
      <el-table-column label="检查类别" align="center" prop="exaM_CLASS" />
      <el-table-column label="检查子类" align="center" prop="exaM_SUB_CLASS" />
      <el-table-column label="项目类别" align="center" prop="desC_ITEM" />
      <el-table-column label="项目代码" align="center" prop="desC_NAME" />
      <el-table-column label="检查项目名称" align="center" prop="description" />
      <el-table-column label="检查项目代码" align="center" prop="descriptioN_CODE" />
      <el-table-column label="拼音简码" align="center" prop="inpuT_CODE" />
      <el-table-column label="检查部位代码" align="center" prop="exaM_POSITION_CODE" />
      <el-table-column label="检查部位名称" align="center" prop="exaM_POSITION_NAME" />
      <el-table-column label="开始时间" align="center" prop="starT_DATE_TIME" />
      <el-table-column label="停用时间" align="center" prop="stoP_DATE_TIME" />
      <el-table-column label="合并单据分组" align="center" prop="mergE_GROUP" />
      <el-table-column label="排斥分组" align="center" prop="repeL_GROUP" />
  
      <el-table-column label="操作" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" icon="el-icon-edit" plain @click="handleEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <div class="empty-box">
          <img :src="emptyImg" alt="暂无数据" class="empty-img" />
          <div class="empty-text">暂无数据</div>
        </div>
      </template>
    </el-table>
    <!-- 分页 -->
  
    <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="1"
      :page-sizes="[10,50, 100, 500, 1000]" :page-size="10" layout="total, sizes, prev, pager, next, jumper"
      :total="total"></el-pagination>
  
    <!-- 新增项目 -->
    <el-dialog title="新增项目" :visible.sync="openAddDialog" width="900px" top="5vh" :modal-append-to-body="false"
      :close-on-click-modal="false" class="custom-add-dialog">
      <!-- 上方表单 -->
      <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="90px" label-position="top"
        class="form-section">
        <div class="form-row">
          <el-form-item label="获取检查分类" prop="inputCode">
            <el-cascader v-model="queryParams.inputCode" :options="searchList" @change=" handleChange" filterable
              class="search-input" clearable placeholder="分类检索..."
              :props="{ label: 'examClassName', value: 'examClassName', children: 'children',expandTrigger: 'hover' }"
              style=" width: 400px;">
            </el-cascader>
          </el-form-item>
        </div>
        <div class="form-row">
  
  
          <el-form-item label="检查类别" prop="exaM_CLASS">
            <el-input v-model="addForm.exaM_CLASS" placeholder="检查类别" clearable />
          </el-form-item>
          <el-form-item label="检查子类" prop="exaM_SUB_CLASS">
            <el-input v-model="addForm.exaM_SUB_CLASS" placeholder="检查子类" clearable />
          </el-form-item>
  
        </div>
  
        <div class="form-row">
          <el-form-item label="项目类别" prop="desC_ITEM">
            <el-input v-model="addForm.desC_ITEM" placeholder="项目类别" clearable />
          </el-form-item>
          <el-form-item label="项目代码" prop="desC_NAME">
            <el-input v-model="addForm.desC_NAME" placeholder="项目代码" clearable />
          </el-form-item>
  
  
        </div>
        <div class="form-row">
  
  
        </div>
        <div class="form-row">
  
          <el-form-item label="检查项目名称" prop="description">
            <el-select v-model="addForm.description" placeholder="下拉选择诊疗项目" clearable @change="handleSelectChange"
              filterable :filter-method="handleFilter" remote :loading="loadingSelect">
              <el-option v-for="(item, index) in filteredSelectDiag" :key="index" :label="item.iteM_NAME"
                :value="item.iteM_NAME" />
            </el-select>
          </el-form-item>
  
          <el-form-item label="检查项目代码" prop="descriptioN_CODE">
            <el-input v-model="addForm.descriptioN_CODE" placeholder="检查项目代码" clearable />
          </el-form-item>
  
  
        </div>
        <div class="form-row">
          <el-form-item label="检查部位代码" prop="exaM_POSITION_CODE">
            <el-input v-model="addForm.exaM_POSITION_CODE" placeholder="检查部位代码" clearable />
          </el-form-item>
          <el-form-item label="检查部位名称" prop="exaM_POSITION_NAME">
            <el-input v-model="addForm.exaM_POSITION_NAME" placeholder="检查部位名称" clearable />
          </el-form-item>
        </div>
        <div class="form-row">
          <el-form-item label="开始时间" prop="starT_DATE_TIME">
            <el-date-picker v-model="addForm.starT_DATE_TIME" type="datetime" placeholder="选择日期时间"
              format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" default-time="12:00:00" clearable>
            </el-date-picker>
  
          </el-form-item>
  
  
          <el-form-item label="停用时间" prop="stoP_DATE_TIME">
            <el-date-picker v-model="addForm.stoP_DATE_TIME" type="datetime" placeholder="选择日期时间"
              format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" default-time="12:00:00" clearable>
            </el-date-picker>
  
          </el-form-item>
          <el-form-item label="合并单据分组" prop="mergE_GROUP">
            <el-input v-model="addForm.mergE_GROUP" placeholder="合并单据分组" type="number" clearable />
          </el-form-item>
          <el-form-item label="排斥分组" prop=" repeL_GROUP">
            <el-input v-model="addForm.repeL_GROUP" placeholder="排斥分组" type="number" clearable />
          </el-form-item>
        </div>
        <div class="form-row">
          <el-form-item label="拼音码" prop="inpuT_CODE">
            <el-input v-model="addForm.inpuT_CODE" placeholder="" disabled />
          </el-form-item>
        </div>
      </el-form>
      <!-- 新增项目按钮 -->
      <div slot="footer">
        <el-button @click="openAddDialog = false">取消</el-button>
        <el-button type="primary" @click="handleAdd">确定</el-button>
      </div>
    </el-dialog>
    <!-- 修改项目 -->
    <el-dialog title="修改项目" :visible.sync="openUpdateDialog" width="900px" top="5vh" :modal-append-to-body="false"
      :close-on-click-modal="false" class="custom-add-dialog">
      <!-- 上方表单 -->
      <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="90px" label-position="top"
        class="form-section">
        <div class="form-row">
          <!-- <el-form-item label="项目类型" prop="ItemClass"><el-select v-model="addForm.ItemClass" placeholder="请选择项目类型"><el-option v-for="item in diagnosisDict" :key="item.value" :label="item.label":value="item.value" /></el-select></el-form-item> -->
  
          <el-form-item label="检查类别" prop="exaM_CLASS">
            <el-input v-model="addForm.exaM_CLASS" placeholder="检查类别" disabled />
          </el-form-item>
          <el-form-item label="检查子类" prop="exaM_SUB_CLASS">
            <el-input v-model="addForm.exaM_SUB_CLASS" placeholder="检查子类" disabled />
          </el-form-item>
          <el-form-item label="项目类别" prop="desC_ITEM">
            <el-input v-model="addForm.desC_ITEM" placeholder="项目类别" disabled />
          </el-form-item>
        </div>
        <div class="form-row">
  
          <el-form-item label="项目代码" prop="desC_NAME">
            <el-input v-model="addForm.desC_NAME" placeholder="项目代码" disabled />
          </el-form-item>
          <el-form-item label="检查项目名称" prop="description">
            <el-select v-model="addForm.description" placeholder="下拉选择诊疗项目" clearable @change="handleSelectChange"
              filterable :filter-method="handleFilter" remote :loading="loadingSelect">
              <el-option v-for="(item, index) in filteredSelectDiag" :key="index" :label="item.iteM_NAME"
                :value="item.iteM_NAME" />
            </el-select>
          </el-form-item>
          <el-form-item label="检查项目代码" prop="descriptioN_CODE">
            <el-input v-model="addForm.descriptioN_CODE" placeholder="检查项目代码" />
          </el-form-item>
        </div>
        <div class="form-row">
  
          <el-form-item label="检查部位代码" prop="exaM_POSITION_CODE">
            <el-input v-model="addForm.exaM_POSITION_CODE" placeholder="检查部位代码" />
          </el-form-item>
          <el-form-item label="检查部位名称" prop="exaM_POSITION_NAME">
            <el-input v-model="addForm.exaM_POSITION_NAME" placeholder="检查部位名称" />
          </el-form-item>
        </div>
        <div class="form-row">
          <el-form-item label="开始时间" prop="starT_DATE_TIME">
            <el-date-picker v-model="addForm.starT_DATE_TIME" type="datetime" placeholder="选择日期时间"
              format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" default-time="12:00:00">
            </el-date-picker>
  
          </el-form-item>
  
  
          <el-form-item label="停用时间" prop="stoP_DATE_TIME">
            <el-date-picker v-model="addForm.stoP_DATE_TIME" type="datetime" placeholder="选择日期时间"
              format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" default-time="12:00:00">
            </el-date-picker>
  
          </el-form-item>
          <el-form-item label="合并单据分组" prop="mergE_GROUP">
            <el-input v-model="addForm.mergE_GROUP" placeholder="合并单据分组" type="number" />
          </el-form-item> <el-form-item label="排斥分组" prop=" repeL_GROUP">
            <el-input v-model="addForm.repeL_GROUP" placeholder="排斥分组" type="number" />
          </el-form-item>
        </div>
        <div class="form-row">
          <el-form-item label="拼音码" prop="inpuT_CODE">
            <el-input v-model="addForm.inpuT_CODE" placeholder="拼音码" />
          </el-form-item>
        </div>
      </el-form>
      <!-- 确定项目按钮 -->
      <div slot="footer">
        <el-button @click="openUpdateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleUpdate">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {

  GainSelectData, GainExamRptPatternData, RemoveExamRptPatternData,
  AddRptPatternData, UpdateRptPatternData, GetSelectDiagDict
} from "@/api/examPattern/index"
import pinyin from 'pinyin'

export default {
  name: 'Index',
  data() {
    return {
      filterIfend: [
        {
          value: '未停用',
          label: '未停用',
        },
        {
          value: '已停用',
          label: '已停用',
        },

      ],
      loadingSelect: false, // 加载中标识
      filteredSelectDiag: [], // 过滤后的选项
      searchKeyword: '', // 搜索关键词
      selectDiag: [],
      searchList: [],
      stasus: '',
      loading: false,
      dateList: [],
      emptyImg: require('@/assets/images/1.png'),
      openUpdateDialog: false,
      openAddDialog: false,
      diagnosisDict: [],
      addForm: {
        exaM_CLASS: undefined,
        exaM_SUB_CLASS: undefined,
        desC_ITEM: undefined,
        desC_NAME: undefined,
        description: undefined,
        descriptioN_CODE: undefined,
        inpuT_CODE: undefined,
        inpuT_CODE_WB: undefined,
        exaM_NOTICE: undefined,
        pB_SUB_CLASS: undefined,
        exaM_POSITION_CODE: undefined,
        exaM_POSITION_NAME: undefined,
        starT_DATE_TIME: undefined,
        stoP_DATE_TIME: undefined,
        mergE_GROUP: undefined,
        repeL_GROUP: undefined,
      },
      addRules: {
        exaM_CLASS: [
          { required: true, message: '检查类别不能为空', trigger: 'blur' }
        ],
        exaM_SUB_CLASS: [
          { required: true, message: '检查子类不能为空', trigger: 'blur' }
        ],
        desC_ITEM: [
          { required: true, message: '项目类别不能为空', trigger: 'blur' }
        ],
        desC_NAME: [
          { required: true, message: '项目代码不能为空', trigger: 'blur' }
        ],
        starT_DATE_TIME: [
          { required: true, message: '启用日期不能为空', trigger: 'blur' }
        ],
      },
      queryParams: {
        inputCode: undefined,
        pageSize: 10,
        pageNum: 1,
        ifend: '未停用',
      },
      total: 0,
    }
  },

  computed: {

  },

  created() {
    this.getList()
    this.getSelect()
  },
  watch: {
    // 当selectDiag更新时，也更新filteredSelectDiag
    selectDiag: {
      immediate: true,
      handler(newVal) {
        this.filteredSelectDiag = newVal;
      }
    }
  },
  methods: {
    getSelect() {

      try {
        GainSelectData(this.queryParams).then(res => {
          if (res && res.data) {
            this.searchList = res.data

          }
        })
      } catch (error) {
        this.$message.error('获取列表失败')
      }

      GetSelectDiagDict(this.queryParams).then(res => {
        if (res && res.data) {
          this.selectDiag = res.data

        }
      })
    },
    handleChange(value) {
      console.log(value);
      this.addForm.exaM_CLASS = value[0]
      this.addForm.exaM_SUB_CLASS = value[1]
    },
    handleFilter(query) {
      if (query !== '') {
        this.loadingSelect = true;
        setTimeout(() => {
          // 使用filter方法过滤选项
          this.filteredSelectDiag = this.selectDiag.filter(item => {
            return (
              (item.iteM_NAME || '').toLowerCase().includes(query.toLowerCase()) ||
              (item.inpuT_CODE || '').toLowerCase().includes(query.toLowerCase())
            );
          });
          this.loadingSelect = false;
        }, 300);
      } else {
        this.filteredSelectDiag = [];
      }
    },
    handleSelectChange(value) {
      if (value) {
        const selectedItem = this.selectDiag.find(item => item.iteM_NAME === value);
        if (selectedItem) {
          this.addForm.description = selectedItem.iteM_NAME;
          this.addForm.descriptioN_CODE = selectedItem.iteM_CODE;
          this.addForm.inpuT_CODE = selectedItem.inpuT_CODE || ''; // 处理空值
        } else {
          // 清空相关字段
          this.addForm.description = '';
          this.addForm.descriptioN_CODE = '';
          this.addForm.inpuT_CODE = '';
        }
      } else {
        // 当清空选择时，重置相关字段
        this.addForm.description = '';
        this.addForm.descriptioN_CODE = '';
        this.addForm.inpuT_CODE = '';
      }
    },
    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        GainExamRptPatternData(this.queryParams).then(res => {
          if (res && res.data) {
            this.dateList = res.data.examRpts
            this.total = res.data.total
          }
        })
      } catch (error) {
        this.$message.error('获取列表失败')
      }
      this.loading = false
    },
    handleSearch() {
      if (this.searchKeyword) {
        this.dateList = this.dateList.filter(item => {
          // 确保item.description和item.inpuT_CODE存在且不为空字符串
          const description = item.description || '';
          const inpuT_CODE = item.inpuT_CODE || '';
          return description.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
            inpuT_CODE.toLowerCase().includes(this.searchKeyword.toLowerCase());
        });
      } else {
        this.getList(); // 清空搜索时重新加载全部数据
      }
    },
    handleSizeChange(val) {

      this.queryParams.pageSize = val;
      this.getList();

    },
    handleCurrentChange(val) {

      this.queryParams.pageNum = val;
      this.getList();

    },
    toAdd() {
      this.stasus = 'add'
      this.addForm = {
        exaM_CLASS: undefined,
        exaM_SUB_CLASS: undefined,
        desC_ITEM: undefined,
        desC_NAME: undefined,
        description: undefined,
        descriptioN_CODE: undefined,
        inpuT_CODE: undefined,
        inpuT_CODE_WB: undefined,
        exaM_NOTICE: undefined,
        pB_SUB_CLASS: undefined,
        exaM_POSITION_CODE: undefined,
        exaM_POSITION_NAME: undefined,
        starT_DATE_TIME: undefined,
        stoP_DATE_TIME: undefined,
        mergE_GROUP: undefined,
        repeL_GROUP: undefined,
      },
        this.openAddDialog = true
    },
    // 新增项目
    handleAdd() {
      console.log(this.addForm);
      this.$refs.addFormRef.validate(valid => {
        if (!valid) return
        // 这里可以调用AddDiagnosis接口进行保存
        AddRptPatternData(this.addForm).then(res => {
          if (res && res.code === 200) {
            this.$message.success('新增成功')
            this.openAddDialog = false
            this.getList && this.getList()
          } else {
            this.$message.error(res.msg || '新增失败')
          }
        })
      })
    },




    // 编辑项目
    handleEdit(row) {
      // 将表格行数据填充到表单中
      //   this.addForm = {
      //     exaM_CLASS: row.exaM_CLASS,
      //     exaM_SUB_CLASS: row.exaM_SUB_CLASS,
      //     desC_ITEM: row.desC_ITEM,
      //     desC_NAME: row.desC_NAME,
      //     description: row.description,
      //     descriptioN_CODE: row.descriptioN_CODE,
      //     inpuT_CODE: row.inpuT_CODE,
      //     inpuT_CODE_WB: row.inpuT_CODE_WB,
      //     exaM_NOTICE: row.exaM_NOTICE,
      //     pB_SUB_CLASS: row.pB_SUB_CLASS,
      //     exaM_POSITION_CODE: row.exaM_POSITION_CODE,
      //     exaM_POSITION_NAME: row.exaM_POSITION_NAME,
      //     starT_DATE_TIME: row.starT_DATE_TIME,
      //     stoP_DATE_TIME: row.stoP_DATE_TIME,
      //     mergE_GROUP: row.mergE_GROUP,
      //     repeL_GROUP: row.repeL_GROUP,
      //     }
      this.stasus = 'update'
      this.addForm = { ...this.addForm, ...row }
      this.openUpdateDialog = true
    },
    handleUpdate() {
      this.$refs.addFormRef.validate(valid => {
        if (!valid) return
        // 这里可以调用AddDiagnosis接口进行保存
        UpdateRptPatternData(this.addForm).then(res => {
          if (res && res.code === 200) {
            this.$message.success('操作成功')
            this.openUpdateDialog = false
            this.getList && this.getList()
          } else {
            this.$message.error(res.msg || '操作成功')
          }
        })
      })
    },


  }
}
</script>

<style lang="scss" scoped>
// 主容器样式
.inherit-platform {
  padding: 24px;
  background-color: #f6f8fa;
  min-height: calc(100vh - 84px);
}

// 页面头部样式
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 8px;
}

.left {
  .title {
    font-size: 24px;
    font-weight: 600;
    color: #1f2329;
    margin-bottom: 8px;
  }
}

.right {
  .el-button {
    padding: 12px 24px;
    font-size: 14px;
    border-radius: 8px;
    background: #409eff;
    border: none;
    color: #fff;
    transition: all 0.3s;

    &:hover {
      background: #66b1ff;
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
    }
  }
}

// 空数据样式
.empty-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;

  .empty-img {
    width: 80px;
    height: 80px;
    margin-bottom: 12px;
    opacity: 0.7;
  }

  .empty-text {
    color: #999;
    font-size: 16px;
  }
}

.custom-add-dialog .el-dialog {
  border-radius: 14px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
}

.custom-add-dialog .el-dialog__header {
  background: #f5f7fa;
  border-radius: 14px 14px 0 0;
  padding: 18px 24px 10px 24px;
}

.custom-add-dialog .el-dialog__body {
  padding: 24px 32px 10px 32px;
  background: #fff;
}

.custom-add-dialog .el-form-item {
  margin-bottom: 22px;
}

.custom-add-dialog .el-input__inner {
  border-radius: 7px;
  height: 38px;
  font-size: 15px;
}

.custom-add-dialog .el-select .el-input__inner {
  border-radius: 7px;
}

.custom-add-dialog .el-dialog__footer {
  padding: 12px 32px 24px 32px;
  background: #f5f7fa;
  border-radius: 0 0 14px 14px;
  text-align: right;
}

.custom-add-dialog .el-button {
  min-width: 80px;
  border-radius: 7px;
  font-size: 15px;
  margin-left: 12px;
}

// 表单区域样式
.form-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.form-row {
  display: flex;
  gap: 10px;
  margin-bottom: 1px;
}

.form-row .el-form-item {
  flex: 1;
  margin-bottom: 0;
}

.form-section .el-form-item {
  margin-bottom: 16px;
}

.form-section .el-input__inner,
.form-section .el-select .el-input__inner {
  border-radius: 6px;
  height: 36px;
  font-size: 14px;
}

// 表格区域样式
.table-section {
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2329;
}

.table-count {
  font-size: 14px;
  color: #86909c;
}

.table-section .el-table {
  border: none;
}

.table-section .el-table th {
  background: #f8f9fa;
  color: #1f2329;
  font-weight: 600;
}

.table-section .el-table td {
  padding: 8px 0;
}
</style>