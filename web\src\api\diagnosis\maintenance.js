import request from '@/utils/request'

// 查询诊疗项目字典数据
export function GetDiagnosisDict() {
    return request({
        url: '/Maintenance/GetDiagnosisDict',
        method: 'get'
    })
}

// 查询诊疗项目数据
export function GetDiagnosisList(query) {
    return request({
        url: '/Maintenance/GetDiagnosisList',
        method: 'get',
        params: query
    })
}

// 新增诊疗项目字典/名称数据
export function AddDiagnosis(data) {
    return request({
        url: '/Maintenance/AddDiagnosis',
        method: 'post',
        data: data
    })
}

// 查询诊疗项目字典/名称数据详情
export function GetDiagnosisDetail(ItemCode) {
    return request({
        url: '/Maintenance/GetDiagnosisDetail?ItemCode=' + ItemCode,
        method: 'get'
    })
}

// 修改诊疗项目字典/名称数据
export function UpdateDiagnosis(data) {
    return request({
        url: '/Maintenance/UpdateDiagnosis',
        method: 'post',
        data: data
    })
}

// 停用诊疗项目
export function DeactivateDiagnosis(data) {
    return request({
        url: '/Maintenance/DeactivateDiagnosis',
        method: 'post',
        data: data
    })
}

// 批量停用诊疗项目
export function BatchDeactivateDiagnosis(data) {
    return request({
        url: '/Maintenance/BatchDeactivateDiagnosis',
        method: 'post',
        data: data
    })
}