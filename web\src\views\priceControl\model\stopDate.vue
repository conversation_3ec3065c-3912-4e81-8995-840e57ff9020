<template>
  <div class="element-master">
    <el-dialog
      title="价格停用"
      :visible.sync="status"
      width="60%">
      <div>
        <el-alert
          title="注意：根据以下选项同步至对应表中信息，同步诊疗项目时，系统会检测是否有和价表相同名字的项目，如果没有则会进行系统提示!!!"
          type="warning"
          show-icon>
        </el-alert>
        <el-alert
          title="注意：批量停用时，系统将同步停用项目表，如果有则停用，没有则跳过"
          type="warning"
          show-icon>
        </el-alert>
        <div class="element-title" v-if="projectType === '1'">
          <div class="va-form">
            <div class="form-item">
              <div class="form-title">
                项目编码：
              </div>
              <div class="form-text" style="color: #3A71A8">
                {{ projectList[0].iteM_CODE }}
              </div>
            </div>
            <div class="form-item">
              <div class="form-title">
                项目名称：
              </div>
              <div class="form-text" style="color: #3A71A8">
                {{ projectList[0].iteM_NAME }}
              </div>
            </div>
          </div>
        </div>
        <div class="element-date">
          <div class="element-date-text">停用时间：</div>
          <el-date-picker
            v-model="stopDate"
            type="datetime"
            placeholder="选择开始日期和时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 280px"/>
        </div>
        <div class="element-radio">
          <el-radio v-model="clinicType" label="1">同步诊疗项目</el-radio>
          <el-radio v-model="clinicVsType" label="1">同步对照关系</el-radio>
        </div>
        <div class="element-button">
          <el-button style="width: 150px;" type="primary" @click="stopPriceClick">价格停用</el-button>
          <el-button style="width: 150px;" type="danger" @click="status = false">取消/关闭</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {StopPriceProject} from '@/api/diagnosis/priceControl'
export default {
  name: 'stopDate',
  props: [],
  components: {},
  data() {
    return {
      projectType: '',
      projectList:[],
      status: false,
      stopDate: '',
      clinicType: '',
      clinicVsType: '',
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    stopPriceClick(){
      if (!this.stopDate){
        this.$msgbox.alert(
          '<div style="font-size: 28px !important;color: red; text-align: center;font-weight: 800;margin-bottom: 10px;">' +
          '请选择停用时间' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        })
        return;
      }
      StopPriceProject( {
        stopDate: this.stopDate,
        clinicType: this.clinicType,
        clinicVsType: this.clinicVsType,
        list: this.projectList,
      }).then(res => {
        if (res.code === 200){
          this.$message.success(res.message);
          this.$emit("stop-success",true)
          this.status = false;
        }
      })
    },
    init(data,type){
      this.projectList = [];
      this.stopDate = '';
      if (type === '2'){
        this.clinicType = '1';
      }else{
        this.clinicType = '';
      }
      this.clinicVsType = '1';
      this.projectList = data;
      this.projectType = type;
      this.status = true;
    },
  },
}
</script>

<style scoped lang="scss">
.element-master{
  .element-title{
    .va-form {
      margin-top: 10px;
      display: flex;
      justify-content: center;

      .form-item {
        width: 40%;
        min-width: 210px;
        display: flex;

        .form-title {
          font-size: 22px;
          font-weight: 800;
        }

        .form-text {
          font-size: 18px;
          display: flex;
          align-items: center;
          font-weight: 500;
        }
      }
    }
  }
  .element-date{
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    .element-date-text{
      font-size: 20px;
      font-weight: 800;
    }
  }
  .element-radio{
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    ::v-deep.el-radio__label {
      font-size: 18px;
      padding-left: 10px;
    }
  }
  .element-button{
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
