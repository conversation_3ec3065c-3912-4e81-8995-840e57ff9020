<template>
  <div>
    <el-dialog
      title="项目名称列表"
      :visible.sync="status"
      width="80%">
      <div style="float: right;">
        <el-button style="width: 150px;margin-bottom: 5px" type="primary" @click="addPriceProjectClick">新增价格名称</el-button>
      </div>

      <el-table :data="tableData" style="width: 100%" border :height="(tableHeight-380)" highlight-current-row>
        <el-table-column type="index" width="40" align="center"></el-table-column>
        <el-table-column property="className" label="项目类别" width="80" align="center" show-overflow-tooltip/>
        <el-table-column property="iteM_CODE" label="项目编号"  align="center" show-overflow-tooltip/>
        <el-table-column property="iteM_NAME" label="项目名称"  align="center" show-overflow-tooltip/>
        <el-table-column label="展示类型"  align="center" width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="scope.row.stD_INDICATOR === 1">默认名称</el-tag>
            <el-tag v-else type="success">额外名称</el-tag>
          </template>
        </el-table-column>
        <el-table-column property="starT_DATE_TIME" label="启用时间" width="180" align="center" show-overflow-tooltip/>
        <el-table-column property="stoP_DATE_TIME" label="停用时间" width="180" align="center" show-overflow-tooltip/>
        <el-table-column label="操作" width="180"  align="center" show-overflow-tooltip>
          <template slot-scope="scope">
<!--            <el-button type="danger" v-if="scope.row.stD_INDICATOR !== 1" icon="el-icon-plus" @click="stopProject">停用</el-button>-->
<!--            <el-button type="success" v-if="scope.row.stD_INDICATOR !== 1" icon="el-icon-plus" @click="startProject">启用</el-button>-->
            <el-button v-if="scope.row.stD_INDICATOR !== 1" type="text" @click="projectOperation(scope.row,'1')">停用</el-button>
            <el-button v-if="scope.row.stD_INDICATOR !== 1" type="text" @click="projectOperation(scope.row,'2')">启用</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex;justify-content: center;align-items: center;margin-top: 20px;">
        <el-button style="width: 150px;" type="danger" @click="status = false">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="operationTitle"
      :visible.sync="operationStatus"
      width="40%">
      <el-alert
        title="注意：价格默认名称不支持进行时间修改，会跟随价格进行变动。!!!"
        type="warning"
        show-icon>
      </el-alert>
      <div class="element-date" v-if="formData.type === '1'">
        <div class="element-date-text">停用时间：</div>
        <el-date-picker
          v-model="formData.stoP_DATE_TIME"
          type="datetime"
          placeholder="选择开始日期和时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 240px"/>
      </div>
      <div class="element-date" v-if="formData.type === '2'">
        <div class="element-date-text">启用时间：</div>
        <el-date-picker
          v-model="formData.starT_DATE_TIME"
          type="datetime"
          placeholder="选择开始日期和时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 240px"/>
      </div>
      <div class="element-button">
        <el-button style="width: 150px;" type="primary" @click="stopOrStartPriceClick">价格停用</el-button>
        <el-button style="width: 150px;" type="danger" @click="operationStatus = false">取消/关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="项目名称新增"
      :visible.sync="addStatus"
      width="40%">
      <div class="add-form">
        <el-form ref="addPriceDetailFormRef" :model="addForm" label-width="120px" label-position="right">
          <el-form-item label="项目编号">
            <el-input style="width: 140px" disabled v-model="addForm.iteM_CODE" placeholder="请输入内容"></el-input>
          </el-form-item>
          <el-form-item label="项目名称" prop="iteM_NAME"
                        :rules="[{ required: true, message: '请输入项目名称', trigger: 'change' }]">
            <el-input style="width: 260px" @input="onNameInput" v-model="addForm.iteM_NAME" placeholder="请输入内容"></el-input>
          </el-form-item>
          <el-form-item label="简拼">
            <el-input style="width: 160px" disabled v-model="addForm.inpuT_CODE" placeholder="请输入内容"></el-input>
          </el-form-item>
          <el-form-item label="启用时间" prop="starT_DATE"
                        :rules="[{ required: true, message: '请选择开始日期和时间', trigger: 'change' }]">
            <el-date-picker
              v-model="addForm.starT_DATE_TIME"
              type="datetime"
              placeholder="选择开始日期和时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 240px"/>
          </el-form-item>
        </el-form>
        <div>
          <div class="element-button">
            <el-button style="width: 150px;" type="primary" @click="addPriceProject">价格名称新增</el-button>
            <el-button style="width: 150px;" type="danger" @click="addStatus = false">取消/关闭</el-button>
          </div>
        </div>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import {GetPriceProjectList,UpdatePriceProject,AddPriceProject} from '@/api/diagnosis/priceProject'
import pinyin from "pinyin";
export default {
  name: 'priceProjectName',
  props: ['tableHeight'],
  components: {},
  data() {
    return {
      status: false,
      operationStatus: false,
      operationTitle: '',
      addStatus: false,
      rowData: {},
      tableData: [],
      formData:{},
      addForm: {},
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    addPriceProject(){
      this.$refs.addPriceDetailFormRef.validate(valid => {
        if (valid) {
          console.log(this.addForm)
          AddPriceProject(this.addForm).then(res => {
            if (res.code === 200){
              this.$message.success("数据新增成功")
              this.getProjectList();
              this.addStatus = false;
            }
          })
        }
      })
    },
    onNameInput(val) {
      if (val) {
        // 取首字母大写输入码
        const pyArr = pinyin(val, {style: pinyin.STYLE_FIRST_LETTER})
        this.addForm.inpuT_CODE = pyArr.map(arr => arr[0]).join('').toUpperCase()
      } else {
        this.addForm.inpuT_CODE = ''
      }
    },
    addPriceProjectClick(){
      this.addForm = {
        iteM_CODE: this.rowData.iteM_CODE,
        iteM_CLASS: this.rowData.iteM_CLASS,
        stD_INDICATOR: 0,
      };
      this.addForm.iteM_CODE = this.rowData.iteM_CODE;
      this.addStatus = true;
    },
    stopOrStartPriceClick(){
      if (this.formData.type === '1'){
        if (!this.formData.stoP_DATE_TIME){
          this.$msgbox.alert(
            '<div style="font-size: 28px !important;color: red; text-align: center;font-weight: 800;margin-bottom: 10px;">' +
            '请选择停用时间' + '</div>',
            '系统提示',
            {
              confirmButtonText: '确定',
              type: 'warning',
              dangerouslyUseHTMLString: true
            }).then(() => {
          })
          return;
        }
      }else{
        if (!this.formData.starT_DATE_TIME){
          this.$msgbox.alert(
            '<div style="font-size: 28px !important;color: red; text-align: center;font-weight: 800;margin-bottom: 10px;">' +
            '请选择启用时间' + '</div>',
            '系统提示',
            {
              confirmButtonText: '确定',
              type: 'warning',
              dangerouslyUseHTMLString: true
            }).then(() => {
          })
          return;
        }
      }
      UpdatePriceProject(this.formData).then(res => {
        if (res.code === 200){
          this.$message.success(res.message)
          this.operationStatus = false;
        }
      })
    },
    projectOperation(row,type){
      this.formData = row;
      this.formData.type = type;
      this.formData.updateStartDate = row.starT_DATE_TIME;
      if (type === '1'){
        this.operationTitle = "计价项目名称停用"
      }else{
        this.operationTitle = "计价项目名称启用"
      }
      this.operationStatus = true;
    },
    init(data){
      this.rowData = data;
      this.getProjectList();
      this.status = true;
    },
    getProjectList(){
      this.tableData = []
      GetPriceProjectList(this.rowData.iteM_CLASS,this.rowData.iteM_CODE).then((res) => {
        if (res.code === 200){
          this.tableData = res.data;
        }
      })
    }
  },
}
</script>

<style scoped lang="scss">
.element-date{
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  .element-date-text{
    font-size: 20px;
    font-weight: 800;
  }
}
.element-button{
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.add-form{

}
</style>
