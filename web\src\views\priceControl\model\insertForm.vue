<template>
  <div class="single-master">
    <el-drawer
      :title="title"
      :visible.sync="status"
      size="1000px">
      <div class="from-master" style="padding: 10px 5px 5px 5px;">
        <el-form ref="addPriceDetailFormRef" :model="formData" label-width="120px" label-position="right">
          <div style="margin-left: 20px;">
            <el-form-item label="项目类别">
              <el-select style="width: 160px" v-model="formData.iteM_CLASS" placeholder="请选择项目类别">
                <el-option
                  v-for="item in dataDict.billItemDict"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"/>
              </el-select>
            </el-form-item>
          </div>
          <el-divider class="divider-title" content-position="center"></el-divider>
          <el-card>
            <div class="form-flex-one">
              <el-form-item label="项目代码">
                <el-input style="width: 160px;" v-model="formData.iteM_CODE"/>
              </el-form-item>
              <el-form-item label="项目名称">
                <el-input style="width: 300px;" v-model="formData.iteM_NAME" @input="onNameInput" show-word-limit
                          maxlength="50"/>
              </el-form-item>
              <el-form-item label="简拼">
                <el-input style="width: 130px;" v-model="formData.inpuT_CODE" show-word-limit autocomplete="off"
                          disabled/>
              </el-form-item>
            </div>
            <div class="form-flex-one">
              <el-form-item label="国家项目编码">
                <el-input style="width: 160px;" @input="materialCodeCharge" v-model="formData.materiaL_CODE" show-word-limit/>
              </el-form-item>
              <el-form-item label="国家项目名称">
                <el-input style="width: 350px;" v-model="formData.materiaL_NAME" show-word-limit maxlength="50"/>
              </el-form-item>
            </div>
            <div class="form-flex-one">
              <el-form-item label="项目规格">
                <el-input style="width: 160px;" v-model="formData.iteM_SPEC"/>
              </el-form-item>
              <el-form-item label="计价单位">
                <el-input style="width: 160px;" v-model="formData.units"/>
              </el-form-item>
            </div>
            <div class="form-flex-one">
              <el-form-item label="医保国码">
                <el-input style="width: 160px;" v-model="formData.natioN_CODE"/>
              </el-form-item>
            </div>
            <div style="border: 1px solid slateblue;margin-bottom: 10px;"></div>
            <div class="form-flex-one">
              <el-form-item label="价格">
                <el-input-number v-model.number="formData.price" @change="priceCharge" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
              <el-form-item label="优惠价格">
                <el-input-number v-model.number="formData.prefeR_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
              <el-form-item label="外宾价格">
                <el-input-number v-model.number="formData.foreigneR_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
            </div>
            <div class="form-flex-one">
              <el-form-item label="启用时间" prop="starT_DATE"
                            :rules="[{ required: true, message: '请选择开始日期和时间', trigger: 'change' }]">
                <el-date-picker
                  v-model="formData.starT_DATE"
                  type="datetime"
                  placeholder="选择开始日期和时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"/>
              </el-form-item>
              <el-form-item label="默认执行科室">
                <el-select clearable v-model="formData.performeD_BY" placeholder="可输入简拼搜索" style="width: 100%"
                           :filter-method="filterOne" filterable>
                  <el-option
                    v-for="item in deptDict"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>
              </el-form-item>
            </div>
            <div class="form-flex-one">
              <el-form-item label="备注">
                <el-input style="width: 650px" v-model="formData.memo" type="textarea"
                          :autosize="{ minRows: 2, maxRows: 3}" placeholder="请输入内容"/>
              </el-form-item>
            </div>
            <div style="border: 1px solid slateblue;margin-bottom: 10px;"></div>
            <div class="form-flex-one">
              <el-form-item label="门诊收费类别">
                <el-select v-model="formData.clasS_ON_OUTP_RCPT" placeholder="请选择门诊收费类别"
                           style="width: 100%">
                  <el-option
                    v-for="item in dataDict.outpDict"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="住院收费类别">
                <el-select v-model="formData.clasS_ON_INP_RCPT" placeholder="请选择住院收费类别"
                           style="width: 100%">
                  <el-option
                    v-for="item in dataDict.inpDict"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="核算项目类别">
                <el-select v-model="formData.clasS_ON_RECKONING" placeholder="请选择核算项目类别"
                           style="width: 100%">
                  <el-option
                    v-for="item in dataDict.reckDict"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>
              </el-form-item>
            </div>
            <div class="form-flex-one">
              <el-form-item label="会计科目类别">
                <el-select v-model="formData.subJ_CODE" placeholder="请选择会计科目类别" style="width: 100%">
                  <el-option
                    v-for="item in dataDict.tallyDict"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="病案首页类别">
                <el-select v-model="formData.clasS_ON_MR" @change="mrCharge" placeholder="请选择病案费用类别" style="width: 100%">
                  <el-option
                    v-for="item in dataDict.mrDict"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="医保支付类别">
                <el-select v-model="formData.STD_CODE_1" placeholder="请选择病案费用类别" style="width: 100%">
                  <el-option
                    v-for="item in stdTree"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>
              </el-form-item>
            </div>
            <div style="border: 1px solid slateblue;margin-bottom: 10px;"></div>
            <el-form-item label="服务产出">
              <el-input v-model="formData.connotation" type="textarea" :rows="2"/>
            </el-form-item>
              <el-form-item label="价格构成">
                <el-input v-model="formData.exclusions" type="textarea" :rows="2"/>
              </el-form-item>
            <el-form-item label="计价说明">
              <el-input v-model="formData.explain" type="textarea" :rows="1"/>
            </el-form-item>
            <div class="form-flex-one">
              <el-form-item label="加收项">
                <el-input style="width: 350px" v-model="formData.additional" type="textarea" :rows="1"/>
              </el-form-item>
              <el-form-item label="扩展项">
                <el-input style="width: 350px" v-model="formData.extensioN_ITEM" type="textarea" :rows="1"/>
              </el-form-item>
            </div>
            <div style="text-align: center; margin-top: 15px;">
              <div style="display: flex;justify-content: center;align-items: center;">
                <div style="display: flex;flex-direction: column;align-items: flex-start;">
                  <el-radio v-model="formData.clinicType" label="1">同步诊疗项目</el-radio>
                  <el-radio v-model="formData.clinicVsType" label="1">同步对照关系</el-radio>
                </div>
                <div>
                  <el-button @click="status = false">取消</el-button>
                  <el-button type="primary" @click="SavePriceDetail">保存</el-button>
                </div>
              </div>

            </div>
          </el-card>
        </el-form>
      </div>

    </el-drawer>
  </div>
</template>

<script>
import pinyin from "pinyin";
import {AddPrice} from '@/api/diagnosis/priceControl'

export default {
  name: 'insertForm',
  props: ['dataDict'],
  components: {},
  data() {
    return {
      status: false,
      formData: {},
      deptDict:[],
      type: '1',
      title: '',
      stdTree: [{
        value: '甲类',
        label: '甲类'
      }, {
        value: '乙类',
        label: '乙类'
      },{
        value: '丙类',
        label: '丙类'
      }],
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    filterOne(query){
      this.formData.deptName = query;
      if (query !== "" || query){
        this.deptDict = this.dataDict.deptDict.filter((item) => {
          if (item.label.toString().indexOf(query) > -1||
            item.inputCode.toUpperCase().toString().indexOf(query.toUpperCase()) > -1){
            return true;
          }
        })
      }else {
        this.deptDict = this.dataDict.deptDict;
      }
    },
    materialCodeCharge(data){
      this.formData.natioN_CODE = data;
    },
    mrCharge(data){
      // let dict = this.dataDict.mrDict;
      // let item = {};
      // dict.forEach(x => {
      //   if (x.value === data){
      //     item = x;
      //   }
      // })
      // console.log(item)
    },
    SavePriceDetail() {
      this.$refs.addPriceDetailFormRef.validate(valid => {
        if (valid) {
          AddPrice(this.formData).then(res => {
            if (res.code === 200){
              this.$message.success("数据新增成功")
              this.$emit("save-success",true)
              this.status = false;
            }
          })
        }
      })
    },
    init(data, type) {
      this.type = type;
      if (type === '1') {
        this.title = "新增价格信息";
        this.formData = {};
      } else {
        this.title = "修改价格信息";
        this.formData = data;
      }
      this.deptDict = this.dataDict.deptDict;
      this.status = true;
    },
    // 项目名称输入自动生成输入码
    onNameInput(val) {
      if (val) {
        // 取首字母大写输入码
        const pyArr = pinyin(val, {style: pinyin.STYLE_FIRST_LETTER})
        this.formData.inpuT_CODE = pyArr.map(arr => arr[0]).join('').toUpperCase()
      } else {
        this.formData.inpuT_CODE = ''
      }
    },
    priceCharge(data) {
      this.formData.prefeR_PRICE = data;
      this.formData.foreigneR_PRICE = data;
    },
  },
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.from-master {
  .form-flex-one {
    display: flex;
  }

  .el-divider {
    background-color: #ecf5ff;
    height: 20px;
    margin: 0px 0;
  }

  .el-divider__text.is-left {
    color: #409eff;
    background: none;
    top: 18px;
    left: -9px;
    font-weight: bold;
  }

  .el-divider__text {
    font-size: 20px;
    background: none;
    top: 18px;
  }

  ::v-deep.el-input.is-disabled .el-input__inner {
    background-color: #fff;
  }
  ::v-deep.el-form-item {
    margin-bottom: 10px;
  }
}
</style>
