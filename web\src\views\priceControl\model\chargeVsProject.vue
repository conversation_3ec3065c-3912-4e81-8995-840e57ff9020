<template>
  <div>
    <el-dialog
      title="价表对照项目列表"
      :visible.sync="status"
      width="80%">
      <el-table :data="tableData" style="width: 100%" border :height="(tableHeight-380)" highlight-current-row>
        <el-table-column type="index" width="40" align="center"></el-table-column>
        <el-table-column label="操作" width="75"  align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button type="text" @click="projectOperation(scope.row)">停用</el-button>
          </template>
        </el-table-column>
        <el-table-column property="itemClassName" label="诊疗项目类别" width="80" align="center" show-overflow-tooltip/>
        <el-table-column property="itemCode" label="诊疗项目编码"  align="center" show-overflow-tooltip/>
        <el-table-column property="itemName" label="诊疗项目名称"  align="center" show-overflow-tooltip/>
        <el-table-column property="chargeItemSpec" label="规格" width="100" align="center" show-overflow-tooltip/>
        <el-table-column property="units" label="单位" width="100" align="center" show-overflow-tooltip/>
        <el-table-column property="amount" label="数量" width="60" align="center" show-overflow-tooltip/>
        <el-table-column property="startDateTime" label="启用时间" width="160" align="center" show-overflow-tooltip/>
        <el-table-column property="stopDateTime" label="停用时间" width="160" align="center" show-overflow-tooltip/>
      </el-table>
    </el-dialog>
    <el-dialog
      title="对照项目停用"
      :visible.sync="operationStatus"
      width="40%">
      <el-alert
        title="注意：当前只提取未停用的计价对照。!!!"
        type="warning"
        show-icon>
      </el-alert>
      <div class="element-date">
        <div class="element-date-text">停用时间：</div>
        <el-date-picker
          v-model="formData.stopDateTime"
          type="datetime"
          placeholder="选择开始日期和时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 240px"/>
      </div>
      <div class="element-button">
        <el-button style="width: 150px;" type="primary" @click="stopVsPriceClick">对照停用</el-button>
        <el-button style="width: 150px;" type="danger" @click="operationStatus = false">取消/关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {GetChargeVaProjectList,StopChargeVaProject} from '@/api/diagnosis/chargeVsProject'
import logo from "../../../layout/components/Sidebar/Logo.vue";
export default {
  name: 'chargeVsProject',
  props: ['tableHeight'],
  components: {},
  data() {
    return {
      status: false,
      rowData: {},
      tableData: [],
      formData:{},
      operationStatus:false
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    stopVsPriceClick(){
      if (!this.formData.stopDateTime){
        this.$msgbox.alert(
          '<div style="font-size: 28px !important;color: red; text-align: center;font-weight: 800;margin-bottom: 10px;">' +
          '请选择停用时间' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        })
        return;
      }
      StopChargeVaProject(this.formData).then(res => {
        if (res.code === 200){
          this.$message.success(res.message);
          this.getChargeVsProjectList();
          this.operationStatus = false;
        }
      })
    },
    projectOperation(row){
      this.formData = row;
      this.operationStatus = true;
    },
    init(data){
      this.rowData = data;
      this.formData = {};
      this.getChargeVsProjectList();
      this.status = true;
    },
    getChargeVsProjectList(){
      this.tableData = [];
      GetChargeVaProjectList(this.rowData.iteM_CLASS,this.rowData.iteM_CODE).then(res => {
        if (res.code === 200){
          this.tableData = res.data;
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
.element-date{
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  .element-date-text{
    font-size: 20px;
    font-weight: 800;
  }
}
.element-button{
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
