﻿import request from '@/utils/request'

// 检查子类

//检查查询数据
export function GetExamSubclassDictInfo(query) {
  return request({
    url: '/ExamSubclassDict/GetExamSubclassDictInfo',
    method: 'get',
    params: query
  })
}

//检查分类 字典
export function GetExamClassNameInfo(query) {
  return request({
    url: '/ExamSubclassDict/GetExamClassNameInfo',
    method: 'get',
    params: query
  })
}

//保存检查子类字典
export function SaveExamSubclassDict(data) {
  return request({
    url: '/ExamSubclassDict/SaveExamSubclassDict',
    method: 'post',
    data: data
  })
}

//修改检查子类字典
export function UpdateExamSubclassDict(data) {
  return request({
    url: '/ExamSubclassDict/UpdateExamSubclassDict',
    method: 'put',
    data: data
  })
}


