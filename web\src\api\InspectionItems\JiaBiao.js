import request from '@/utils/request'

//查询价表列表
export function GetJiaBiaoList(query) {
  return request({
    url: '/JiaBiao/GetJiaBiaoList',
    method: 'get',
    params: query
  })
}


//更新价格项
export function EditPriceList(query) {
  return request({
    url: '/JiaBiao/EditPriceList',
    method: 'get',
    params: query
  })
}

//停用价格项
export function DeletePriceList(query) {
  return request({
    url: '/JiaBiao/DeletePriceList',
    method: 'get',
    params: query
  })
}

//新增行
export function AddAllPriceList(query) {
  return request({
    url: '/JiaBiao/AddAllPriceList',
    method: 'get',
    params: query
  })
}



//仅修改价格项
export function updateOnlyPrice(query) {
  return request({
    url: '/JiaBiao/updateOnlyPrice',
    method: 'get',
    params: query
  })
}


//仅修改价格项
export function updatePriceNameDictByItemNameItemClass(query) {
  return request({
    url: '/JiaBiao/updatePriceNameDictByItemNameItemClass',
    method: 'get',
    params: query
  })
}



//新增名称字典
export function SaveAllPriceNameDictSel(data) {
  return request({
    url: '/JiaBiao/SaveAllPriceNameDictSel',
    method: 'post',
    data: data
  })
}

export function DeletePriceNameDictAll(query) {
  return request({
    url: '/JiaBiao/DeletePriceNameDictAll',
    method: 'get',
    params: query
  })
}



export function AddBasePriceListToDict(query) {
  return request({
    url: '/JiaBiao/AddBasePriceListToDict',
    method: 'get',
    params: query
  })
}

//生成诊疗项目
export function AddZhenLiaoProject(query) {
  return request({
    url: '/JiaBiao/AddZhenLiaoProject',
    method: 'get',
    params: query
  })
}


//生成对照
export function AddDuiZhao(query) {
  return request({
    url: '/JiaBiao/AddDuiZhao',
    method: 'get',
    params: query
  })
}



