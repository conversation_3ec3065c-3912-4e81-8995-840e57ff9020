<template>
  <div>
    <el-drawer
      title="修改价格信息"
      :visible.sync="status"
      direction="btt"
      size="600px"
    >
      <el-alert
        title="注意：修改价格后将舍弃原有的数据，停止时间默认为当前时间，开始时间参考当前数据开始时间进行赋值，其他数据修改不会新增额外数据，修改后点击空白区域即可回写数据库"
        type="warning"
        show-icon>
      </el-alert>
      <div class="from-master" style="padding: 10px 5px 5px 5px;">
        <el-form ref="updatePriceDetailFormRef" :model="updateForm" label-width="120px" label-position="right">
          <div style="margin-left: 20px;" class="form-flex-one">
            <el-form-item label="项目类别">
              <el-select style="width: 160px" v-model="updateForm.iteM_CLASS" placeholder="请选择项目类别"
                         @change="importantReminderClick('1','您当前正在修改项目类别,请选择以一下类型,选择后系统会进行检测,检测通过后将同步更新,如果不选择只修改价表信息,如果系统报错,请联系信息部进行手动调整!!!')">
                <el-option
                  v-for="item in dataDict.billItemDict"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="默认执行科室">
              <el-select style="width: 160px" clearable v-model="updateForm.performeD_BY" placeholder="可输入简拼搜索"
                         :filter-method="filterOne" @change="updatePrice('2')" filterable>
                <el-option
                  v-for="item in deptDict"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"/>
              </el-select>
            </el-form-item>
          </div>
          <el-divider class="divider-title" content-position="center"></el-divider>
          <el-card style="height: 440px;">
            <div class="form-flex">
              <div class="form-left">
                <div class="form-flex-one" style="margin-top: 10px;">
                  <el-form-item label="住院收费类别">
                    <el-select v-model="updateForm.clasS_ON_INP_RCPT" placeholder="请选择住院收费类别"
                               style="width: 100%" @change="updatePrice('3')">
                      <el-option
                        v-for="item in dataDict.inpDict"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"/>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="门诊收费类别">
                    <el-select v-model="updateForm.clasS_ON_OUTP_RCPT" placeholder="请选择门诊收费类别"
                               style="width: 100%" @change="updatePrice('4')">
                      <el-option
                        v-for="item in dataDict.outpDict"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"/>
                    </el-select>
                  </el-form-item>
                </div>
                <div class="form-flex-one">
                  <el-form-item label="核算项目类别">
                    <el-select v-model="updateForm.clasS_ON_RECKONING" placeholder="请选择核算项目类别"
                               style="width: 100%" @change="updatePrice('5')">
                      <el-option
                        v-for="item in dataDict.reckDict"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"/>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="会计科目类别">
                    <el-select v-model="updateForm.subJ_CODE" placeholder="请选择会计科目类别"
                               style="width: 100%" @change="updatePrice('6')">
                      <el-option
                        v-for="item in dataDict.tallyDict"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"/>
                    </el-select>
                  </el-form-item>
                </div>
                <div class="form-flex-one">
                  <el-form-item label="医保支付类别">
                    <el-select v-model="updateForm.stD_CODE_1" placeholder="请选择病案费用类别"
                               style="width: 100%" @change="updatePrice('7')">
                      <el-option
                        v-for="item in stdTree"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"/>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="病案首页类别">
                    <el-select v-model="updateForm.clasS_ON_MR" placeholder="请选择病案费用类别"
                               style="width: 100%" @change="updatePrice('8')">
                      <el-option
                        v-for="item in dataDict.mrDict"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"/>
                    </el-select>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="物价编码">
                    <el-input style="width: 240px;" @blur="importantReminderClick('9','您当前正在修改物价编码,请选择以一下类型,选择后系统会进行检测,检测通过后将同步更新,如果不选择只修改价表信息,如果系统报错,请联系信息部进行手动调整!!!')" v-model="updateForm.iteM_CODE"/>
                  </el-form-item>
                  <el-form-item label="物价名称">
                    <el-input style="width: 300px;" v-model="updateForm.iteM_NAME" @input="onNameInput" show-word-limit
                              maxlength="50" @blur="importantReminderClick('10','您当前正在修改物价名称,请选择以一下类型,选择后系统会进行检测,检测通过后将同步更新,如果不选择只修改价表信息,如果系统报错,请联系信息部进行手动调整!!!')"/>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="服务产出">
                    <el-input style="width: 440px" v-model="updateForm.connotation" type="textarea" :rows="2" @blur="updatePrice('11')"/>
                  </el-form-item>
                  <el-form-item label="价格构成">
                    <el-input style="width: 440px" v-model="updateForm.exclusions" type="textarea" :rows="2" @blur="updatePrice('12')"/>
                  </el-form-item>
                  <el-form-item label="计价说明">
                    <el-input style="width: 440px" v-model="updateForm.explain" type="textarea" :rows="1" @blur="updatePrice('13')"/>
                  </el-form-item>
                </div>
              </div>
              <div class="form-right">
                <div class="form-flex-one" style="margin-top: 10px;">
                  <el-form-item label="计价规格">
                    <el-input style="width: 160px;" v-model="updateForm.iteM_SPEC" @blur="importantReminderClick('14','您当前正在修改计价规格,请选择以一下类型,选择后系统会进行检测,检测通过后将同步更新,如果不选择只修改价表信息,如果系统报错,请联系信息部进行手动调整!!!')"/>
                  </el-form-item>
                  <el-form-item label="计价单位">
                    <el-input style="width: 160px;" v-model="updateForm.units" @blur="importantReminderClick('15','您当前正在修改计价单位,请选择以一下类型,选择后系统会进行检测,检测通过后将同步更新,如果不选择只修改价表信息,如果系统报错,请联系信息部进行手动调整!!!')"/>
                  </el-form-item>
                </div>
                <div class="form-flex-one">
                  <el-form-item label="国家项目编码">
                    <el-input style="width: 190px;" @input="materialCodeCharge" v-model="updateForm.materiaL_CODE"
                              show-word-limit @blur="updatePrice('16')"/>
                  </el-form-item>
                  <el-form-item label="医保国码">
                    <el-input style="width: 190px;" v-model="updateForm.natioN_CODE" @blur="updatePrice('17')"/>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="国家项目名称">
                    <el-input style="width: 350px;" v-model="updateForm.materiaL_NAME" @blur="updatePrice('18')" show-word-limit maxlength="50"/>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="价格">
                    <el-input-number v-model.number="updateForm.price" @change="priceCharge" :precision="2" :step="0.1"
                                     style="width: 230px;" @blur="updatePrice('19')"/>
                  </el-form-item>
                </div>
                <div class="form-flex-one">
                  <el-form-item label="优惠价格">
                    <el-input-number v-model.number="updateForm.prefeR_PRICE" :precision="2" :step="0.1"
                                     style="width: 100%" @blur="updatePrice('20')"/>
                  </el-form-item>
                  <el-form-item label="外宾价格">
                    <el-input-number v-model.number="updateForm.foreigneR_PRICE" :precision="2" :step="0.1"
                                     style="width: 100%" @blur="updatePrice('21')"/>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="备注">
                    <el-input style="width: 440px" v-model="updateForm.memo" type="textarea"
                              :autosize="{ minRows: 2, maxRows: 3}" placeholder="请输入内容" @blur="updatePrice('22')"/>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="加收项">
                    <el-input style="width: 350px" v-model="updateForm.additional" type="textarea" :rows="1" @blur="updatePrice('23')"/>
                  </el-form-item>
                  <el-form-item label="扩展项">
                    <el-input style="width: 350px" v-model="updateForm.extensioN_ITEM" type="textarea" :rows="1"@blur="updatePrice('24')"/>
                  </el-form-item>
                </div>
                <div style="display: flex;justify-content: center;align-items: center">
                  <el-button type="warning" @click="updateClose">确认/关闭</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-form>
      </div>
    </el-drawer>
    <div class="">
      <el-dialog
        title="重要数据修改提醒"
        :visible.sync="importantReminderStatus"
        width="60%">
        <div style="font-size: 22px;color: red;padding: 10px;">{{ importantReminderText }}</div>
        <div class="dialog-radio" v-if="importantReminderType === '1' || importantReminderType === '9'">
          <el-radio v-model="updateForm.clinicType" label="1">同步诊疗项目</el-radio>
          <el-radio v-model="updateForm.clinicVsType" label="1">同步对照关系</el-radio>
        </div>
        <div class="dialog-radio" v-if="importantReminderType === '10'">
          <el-radio v-model="updateForm.clinicType" label="1">同步诊疗项目</el-radio>
        </div>
        <div class="dialog-radio" v-if="importantReminderType === '14' || importantReminderType === '15'">
          <el-radio v-model="updateForm.clinicVsType" label="1">同步对照关系</el-radio>
        </div>
        <div style="display: flex;justify-content:center;align-items: center;margin-top: 15px;">
          <el-button style="width: 120px" type="primary" @click="updatePrice(importantReminderType)">确认</el-button>
          <el-button style="width: 120px" type="danger" @click="importantReminderStatus = false">取消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import pinyin from "pinyin";
import {DynamicConditionUpdatePrice} from '@/api/diagnosis/priceControl'

export default {
  name: 'updateForm',
  props: ['dataDict'],
  components: {},
  data() {
    return {
      status: false,
      updateForm: {},
      deptDict: [],
      stdTree: [{
        value: '甲类',
        label: '甲类'
      }, {
        value: '乙类',
        label: '乙类'
      }, {
        value: '丙类',
        label: '丙类'
      }],
      importantReminderStatus: false,
      importantReminderText: '',
      importantReminderType: '',
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    importantReminderClick(type, text) {
      this.updateForm.clinicType = "";
      this.updateForm.clinicVsType = "";
      this.importantReminderType = type;
      this.importantReminderText = text;
      this.importantReminderStatus = true;
    },
    updateClose() {
      this.$emit("update-refresh", true)
      this.status = false;
    },
    updatePrice(type) {
      this.updateForm.updateType = type;
      DynamicConditionUpdatePrice(this.updateForm).then(res => {
        if (res.code === 200) {
          this.$message.success(res.message);
          let array = ['1','9','14','15','19'];
          if (array.includes(type)){
            this.updateClose();
            this.$message.warning("重要数据已更新,请重新打开修改页面")
          }
          this.updateForm.clinicType = "";
          this.updateForm.clinicVsType = "";
          this.importantReminderType = "";
          this.importantReminderText = "";
          this.importantReminderStatus = false;
        }
      })
    },
    onNameInput(val) {
      if (val) {
        // 取首字母大写输入码
        const pyArr = pinyin(val, {style: pinyin.STYLE_FIRST_LETTER})
        this.updateForm.inpuT_CODE = pyArr.map(arr => arr[0]).join('').toUpperCase()
      } else {
        this.updateForm.inpuT_CODE = ''
      }
    },
    materialCodeCharge(data) {
      this.updateForm.natioN_CODE = data;
    },
    mrCharge(data) {
      // let dict = this.dataDict.mrDict;
      // let item = {};
      // dict.forEach(x => {
      //   if (x.value === data){
      //     item = x;
      //   }
      // })
      // console.log(item)
    },
    filterOne(query) {
      this.updateForm.deptName = query;
      if (query !== "" || query) {
        this.deptDict = this.dataDict.deptDict.filter((item) => {
          if (item.label.toString().indexOf(query) > -1 ||
            item.inputCode.toUpperCase().toString().indexOf(query.toUpperCase()) > -1) {
            return true;
          }
        })
      } else {
        this.deptDict = this.dataDict.deptDict;
      }
    },
    priceCharge(data) {
      this.updateForm.prefeR_PRICE = data;
      this.updateForm.foreigneR_PRICE = data;
    },
    init(data) {
      if (data) {
        this.updateForm = data;
        this.updateForm.updateClass = data.iteM_CLASS
        this.updateForm.updateItemCode = data.iteM_CODE
        this.updateForm.updateItemSpec = data.iteM_SPEC
        this.updateForm.updateUnits = data.units
        this.updateForm.updateStartDate = data.starT_DATE
        this.deptDict = this.dataDict.deptDict;
        this.status = true;
        console.log(this.updateForm)
      }
    },
  },
}
</script>

<style scoped lang="scss">
.from-master {
  .form-flex-one {
    display: flex;
  }

  .form-flex {
    display: flex;
    height: 473px;

    .form-left {
      width: 50%;
    }

    .form-right {
      width: 50%;
    }
  }

  .el-divider {
    background-color: #ecf5ff;
    height: 10px;
    margin: 0px 0;
  }

  .el-divider__text.is-left {
    color: #409eff;
    background: none;
    top: 18px;
    left: -9px;
    font-weight: bold;
  }

  .el-divider__text {
    font-size: 20px;
    background: none;
    top: 18px;
  }

  ::v-deep.el-input.is-disabled .el-input__inner {
    background-color: #fff;
  }

  ::v-deep.el-form-item {
    margin-bottom: 10px;
  }

  ::v-deep.el-card__body {
    padding: 0px 7px 0px 7px;
  }
}
.dialog-radio {
  display: flex;
  justify-content: center;
  align-items: center;

  ::v-deep.el-radio__label {
    font-size: 20px;
    padding-left: 10px;
  }
}
</style>
