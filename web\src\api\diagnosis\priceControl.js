import request from '@/utils/request'

export function GetPricePageList(data) {
  return request({
    url: '/Price/GetPricePageList',
    method: 'post',
    data: data
  })
}

export function GetPriceCorrelationDict() {
  return request({
    url: '/Price/GetPriceCorrelationDict',
    method: 'get',
  })
}

export function AddPrice(data) {
  return request({
    url: '/Price/AddPrice',
    method: 'post',
    data: data,
  })
}

export function DynamicConditionUpdatePrice(data) {
  return request({
    url: '/Price/DynamicConditionUpdatePrice',
    method: 'put',
    data:data,
  })
}

export function StopPriceProject(data) {
  return request({
    url: '/Price/StopPriceProject',
    method: 'post',
    data:data,
  })
}

export function GetPriceListTree(itemClass) {
  return request({
    url: '/Price/GetPriceListTree?itemClass=' + itemClass,
    method: 'get',
  })
}



