<template>
    <!-- 诊疗项目字典维护 -->
    <div class="inherit-platform">
        <div class="page-header">
            <div class="left">
                <div class="title">诊疗项目</div>
                <div class="subtitle">诊疗项目字典/名称维护</div>
            </div>
            <div class="right">
                <el-select v-model="searchText.iteM_CLASS" placeholder="请选择项目类型" @input="getList" filterable clearable
                    style="width: 220px; margin-top: 10px;margin-right: 5px;">
                    <el-option v-for="item in diagnosisDict" :key="item.value" :label="item.label"
                        :value="item.label" />
                </el-select>
                <el-input v-model="searchText.iteM_NAME" placeholder="搜索项目名称" clearable
                    style="width: 220px; margin-top: 10px;margin-right: 5px;" @input="getList" />
                <el-input v-model="searchText.iteM_CODE" placeholder="搜索项目代码" clearable
                    style="width: 220px; margin-top: 10px;margin-right: 5px;" @input="getList" />
                <el-button type="primary" @click="handleOpenAddDialog">新增</el-button>
                <el-button type="danger" :disabled="selectedRows.length === 0" @click="handleBatchStop">批量停用</el-button>
            </div>
        </div>

        <el-table v-loading="loading" :data="filteredTable" border stripe highlight-current-row style="width: 100%"
            :height="tableHeight" :max-height="600" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column type="index" :index="indexMethod" label="序号" align="center" />
            <el-table-column prop="clasS_NAME" label="项目类型" align="center" />
            <el-table-column prop="iteM_CODE" label="项目代码" align="center" />
            <el-table-column prop="iteM_NAME" label="项目名称" align="center" />
            <el-table-column prop="starT_DATE_TIME" label="启用时间" align="center" />
            <el-table-column prop="stoP_DATE_TIME" label="停用时间" align="center" />
            <el-table-column label="操作" align="center" fixed="right" width="300px">
                <template slot-scope="scope">
                    <el-button size="mini" type="primary" icon="el-icon-edit" plain
                        @click="handleEdit(scope.row)">修改</el-button>
                    <el-button size="mini" type="danger" v-if="!scope.row.stoP_DATE_TIME" icon="el-icon-folder-delete"
                        plain @click="stopClick(scope.row)">停用</el-button>
                    <el-button size="mini" type="info" icon="el-icon-s-operation" plain
                        @click="valuationClick(scope.row)">项目对照</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <div class="empty-box">
                    <img :src="emptyImg" alt="暂无数据" class="empty-img" />
                    <div class="empty-text">暂无数据</div>
                </div>
            </template>
        </el-table>
        <pagination v-show="total > 0" :limit.sync="searchText.pageSize" :page.sync="searchText.pageNum" :total="total"
            @pagination="getList" :page-sizes="[10, 20, 50, 100, 200, 500]" />

        <!-- 新增项目 -->
        <el-dialog :title="isEdit ? '修改项目' : '新增项目'" :visible.sync="openAddDialog" width="900px" top="5vh"
            :modal-append-to-body="false" :close-on-click-modal="false" class="custom-add-dialog"
            @open="fetchDiagnosisDict">
            <!-- 上方表单 -->
            <span class="table-title">诊疗项目字典维护</span>
            <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="90px" label-position="top"
                class="form-section">
                <div class="form-row">
                    <el-form-item label="项目类型" prop="ItemClass">
                        <el-select v-model="addForm.ItemClass" placeholder="请选择项目类型" @change="onFormChange">
                            <el-option v-for="item in diagnosisDict" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="项目代码" prop="ItemCode">
                        <el-input v-model="addForm.ItemCode" placeholder="请输入项目代码" @input="onFormChange" />
                    </el-form-item>
                    <el-form-item label="项目名称" prop="ItemName">
                        <el-input v-model="addForm.ItemName" placeholder="请输入项目名称" @input="onNameInput" />
                    </el-form-item>
                </div>
                <div class="form-row">
                    <el-form-item label="拼音码" prop="InputCode">
                        <el-input v-model="addForm.InputCode" placeholder="自动生成拼音码" disabled />
                    </el-form-item>
                    <el-form-item label="启用时间" prop="startDateTime">
                        <el-date-picker v-model="addForm.startDateTime" type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择启用时间" style="width: 100%;"
                            @change="onFormChange" />
                    </el-form-item>
                    <el-form-item label="停止时间" prop="stopDateTime" v-if="!isEdit">
                        <el-date-picker v-model="addForm.stopDateTime" type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择停止时间" style="width: 100%;"
                            @change="onFormChange" />
                    </el-form-item>
                </div>
            </el-form>
            <!-- 下方表格 -->
            <div class="table-section">
                <div class="table-header">
                    <span class="table-title">诊疗项目名称维护</span>
                    <div class="table-actions">
                        <el-button size="mini" type="success" icon="el-icon-plus"
                            @click="addFormToTable">同步表单数据到表格</el-button>
                        <el-button size="mini" type="primary" icon="el-icon-plus" @click="addTableRow">新增行</el-button>
                        <span class="table-count">共 {{ filteredTableData.length }} 条</span>
                    </div>
                </div>
                <el-table :data="filteredTableData" border stripe highlight-current-row style="width: 100%"
                    height="300">
                    <el-table-column type="index" label="序号" align="center" width="60" />
                    <el-table-column label="项目类型" align="center" width="150">
                        <template slot-scope="scope">
                            <el-select v-model="scope.row.ItemClass" placeholder="请选择" size="mini"
                                @change="onTableRowChange">
                                <el-option v-for="item in diagnosisDict" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="项目代码" align="center" width="150">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.ItemCode" placeholder="请输入" size="mini" disabled />
                        </template>
                    </el-table-column>
                    <el-table-column label="项目名称" align="center" width="200">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.ItemName" placeholder="请输入" size="mini"
                                @input="onTableNameInput(scope.row)" />
                        </template>
                    </el-table-column>
                    <el-table-column label="拼音码" align="center" width="120">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.InputCode" placeholder="自动生成" size="mini" disabled />
                        </template>
                    </el-table-column>
                    <el-table-column label="启用时间" align="center" width="180">
                        <template slot-scope="scope">
                            <el-date-picker v-model="scope.row.startDateTime" type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择启用时间" size="mini"
                                style="width: 100%;" />
                        </template>
                    </el-table-column>
                    <el-table-column label="停止时间" align="center" width="180">
                        <template slot-scope="scope">
                            <el-date-picker v-model="scope.row.stopDateTime" type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择停止时间" size="mini"
                                style="width: 100%;" />
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" fixed="right" width="150">
                        <template slot-scope="scope">
                            <el-button size="mini" type="primary" icon="el-icon-edit" plain
                                @click="handleEditRow(scope.row)">同步</el-button>
                            <el-button size="mini" type="danger" icon="el-icon-delete" plain
                                @click="handleDelete(scope.$index)">删除</el-button>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <div class="empty-box">
                            <img :src="emptyImg" alt="暂无数据" class="empty-img" />
                            <div class="empty-text">暂无数据</div>
                        </div>
                    </template>
                </el-table>
            </div>
            <!-- 新增项目按钮 -->
            <div slot="footer">
                <el-button @click="openAddDialog = false">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
            </div>
        </el-dialog>
        <el-dialog title="请选择项目停用时间" :visible.sync="stopStatus" width="30%">
            <div style="margin-top: 10px;margin-bottom: 10px;text-align: center">
                <el-date-picker v-model="stopDate" type="datetime" placeholder="选择日期时间">
                </el-date-picker>
            </div>
            <div style="text-align: center; margin: 3px">
                <el-button @click="stopStatus = false">取消</el-button>
                <el-button type="primary" @click="handleClose">确定</el-button>
            </div>
        </el-dialog>
        <div>
            <valuation-contrast ref="valuationContrastRefs"></valuation-contrast>
        </div>
    </div>
</template>

<script>
import {
    GetDiagnosisDict, GetDiagnosisList, AddDiagnosis,
    GetDiagnosisDetail, UpdateDiagnosis, DeactivateDiagnosis
} from "@/api/diagnosis/maintenance"
import pinyin from 'pinyin'
import ValuationContrast from "./model/valuationContrast.vue";

export default {
    name: 'Index',
    components: { ValuationContrast },
    data() {
        return {
            loading: false,
            tableData: [],
            emptyImg: require('@/assets/images/1.png'),
            openAddDialog: false,
            diagnosisDict: [],
            dialogTableData: [], // 弹框中的表格数据
            stopDate: '',
            stopStatus: false,
            stopData: {},
            addForm: {
                ItemClass: '',
                ItemCode: '',
                ItemName: '',
                InputCode: '',
                startDateTime: '',
                stopDateTime: ''
            },
            addRules: {
                ItemClass: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
                ItemCode: [{ required: true, message: '请输入项目代码', trigger: 'blur' }],
                ItemName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
                startDateTime: [{ required: true, message: '请选择启用时间', trigger: 'change' }],
                stopDateTime: [
                    {
                        validator: (rule, value, callback) => {
                            if (value && this.addForm.startDateTime && value <= this.addForm.startDateTime) {
                                callback(new Error('停止时间必须大于启用时间'));
                            } else {
                                callback();
                            }
                        },
                        trigger: 'change'
                    }
                ]
            },
            isEdit: false,
            searchText: {
                iteM_NAME: '',
                pageNum: 1,
                pageSize: 10
            }, // 搜索文本
            total: 0,
            filteredTable: [], // 新增过滤后的表格数据
            tableHeight: 500, // 表格固定高度
            selectedRows: [], // 选中的行数据
            batchStopStatus: false, // 批量停用弹框状态
            batchStopDate: '', // 批量停用时间
        }
    },

    computed: {
        // 表格数据
        filteredTableData() {
            // 始终显示弹框表格数据，不再同步表单数据
            return this.dialogTableData
        }
    },

    created() {
        this.getList()
        this.fetchDiagnosisDict()
        this.calculateTableHeight()
    },

    mounted() {
        window.addEventListener('resize', this.calculateTableHeight)
    },

    beforeDestroy() {
        window.removeEventListener('resize', this.calculateTableHeight)
    },

    methods: {
        valuationClick(row) {
            this.$refs.valuationContrastRefs.init(row);
        },
        // 获取列表数据
        async getList() {
            this.loading = true
            try {
                const res = await GetDiagnosisList(this.searchText)
                this.tableData = res.data.data
                this.filteredTable = res.data.data // 初始化过滤表格
                this.total = res.data.total
            } catch (error) {
                this.$message.error('获取列表失败')
            }
            this.loading = false
        },

        // 获取诊疗项目字典数据
        fetchDiagnosisDict() {
            GetDiagnosisDict().then(res => {
                if (res && res.data) {
                    this.diagnosisDict = res.data.map(item => ({
                        label: item.clasS_NAME,
                        value: item.clasS_CODE
                    }))
                }
            })
        },

        // 表单数据变化事件
        onFormChange() {
            // 表单项目代码变化时，同步所有表格行的项目代码
            this.dialogTableData.forEach(row => {
                row.ItemCode = this.addForm.ItemCode
            })
            // 验证停止时间
            this.$nextTick(() => {
                this.$refs.addFormRef.validateField('stopDateTime');
            });
        },

        // 修改项目
        async handleEdit(row) {
            // 1. 获取详情
            try {
                const res = await GetDiagnosisDetail(row.iteM_CODE)
                if (res && res.code === 200 && res.data) {
                    const main = res.data.mainInfo || {}
                    // 2. 映射主表单字段
                    this.addForm = {
                        ItemClass: main.iteM_CLASS || '',
                        ItemCode: main.iteM_CODE || '',
                        ItemName: main.iteM_NAME || '',
                        InputCode: main.inpuT_CODE || '',
                        startDateTime: main.starT_DATE_TIME || '',
                        stopDateTime: main.stoP_DATE_TIME || ''
                    }
                    // 3. 映射子表格字段
                    this.dialogTableData = (res.data.subInfos || []).map(item => ({
                        ItemClass: item.iteM_CLASS || '',
                        ItemCode: item.iteM_CODE || '',
                        ItemName: item.iteM_NAME || '',
                        InputCode: item.inpuT_CODE || '',
                        startDateTime: item.starT_DATE_TIME || '',
                        stopDateTime: item.stoP_DATE_TIME || ''
                    }))
                    this.isEdit = true
                    this.openAddDialog = true
                } else {
                    this.$message.error(res.message || '获取详情失败')
                }
            } catch (e) {
                this.$message.error('获取详情失败')
            }
        },

        // 新增项目
        handleOpenAddDialog() {
            // 获取当前时间并格式化为 yyyy-MM-dd HH:mm:ss
            const now = new Date();
            const pad = n => n < 10 ? '0' + n : n;
            const format = d => `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`;
            this.addForm = {
                ItemClass: '',
                ItemCode: '',
                ItemName: '',
                InputCode: '',
                startDateTime: format(now), // 默认当前时间
                stopDateTime: ''
            }
            this.dialogTableData = [] // 只在新增时清空
            this.isEdit = false
            this.openAddDialog = true
        },

        // 新增/修改项目提交
        handleSubmit() {
            this.$refs.addFormRef.validate(valid => {
                if (!valid) return
                const formData = { ...this.addForm }
                // 如果为空字符串或null，删除时间字段
                if (!formData.startDateTime) formData.startDateTime = null
                if (!formData.stopDateTime) formData.stopDateTime = null
                // 处理表格数据
                const tableData = this.dialogTableData.map(row => {
                    const newRow = { ...row }
                    if (!newRow.startDateTime) newRow.startDateTime = null
                    if (!newRow.stopDateTime) newRow.stopDateTime = null
                    return newRow
                })
                const submitData = {
                    formData,
                    tableData
                }
                if (this.isEdit) {
                    UpdateDiagnosis(submitData).then(res => {
                        if (res && res.code === 200) {
                            this.$message.success('修改成功')
                            this.openAddDialog = false
                            this.getList()
                        } else {
                            this.$message.error(res.msg || '修改失败')
                        }
                    })
                } else {
                    AddDiagnosis(submitData).then(res => {
                        if (res && res.code === 200) {
                            this.$message.success('新增成功')
                            this.openAddDialog = false
                            this.getList()
                        } else {
                            this.$message.error(res.msg || '新增失败')
                        }
                    })
                }
            })
        },

        // 新增表格行
        addTableRow() {
            // 添加新的空行数据，ItemCode 与表单同步
            this.dialogTableData.push({
                ItemClass: '',
                ItemCode: this.addForm.ItemCode, // 保持一致
                ItemName: '',
                InputCode: '',
                startDateTime: this.addForm.startDateTime,
                stopDateTime: this.addForm.stopDateTime
            })
            this.$message.success('新增行成功')
        },

        // 删除表格行
        handleDelete(index) {
            this.$confirm('确定要删除这一行吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.dialogTableData.splice(index, 1)
                this.$message.success('删除成功')
            }).catch(() => {
                // 取消删除
            })
        },

        // 表格行数据变化事件
        onTableRowChange() { },

        // 表格名称输入事件
        onTableNameInput(row) {
            if (row.ItemName) {
                // 取首字母大写拼音码
                const pyArr = pinyin(row.ItemName, { style: pinyin.STYLE_FIRST_LETTER })
                row.InputCode = pyArr.map(arr => arr[0]).join('').toUpperCase()
            } else {
                row.InputCode = ''
            }
        },

        // 添加表单数据到表格
        addFormToTable() {
            if (this.addForm.ItemClass && this.addForm.ItemCode && this.addForm.ItemName) {
                this.dialogTableData.push({
                    ItemClass: this.addForm.ItemClass,
                    ItemCode: this.addForm.ItemCode, // 保持一致
                    ItemName: this.addForm.ItemName,
                    InputCode: this.addForm.InputCode,
                    startDateTime: this.addForm.startDateTime,
                    stopDateTime: this.addForm.stopDateTime
                })
                // 不清空表单，让用户可以继续添加相同的数据
                this.$message.success('表单数据已添加到表格')
            } else {
                this.$message.warning('请先填写完整的表单信息')
            }
        },

        // 项目名称输入自动生成拼音码
        onNameInput(val) {
            if (val) {
                // 取首字母大写拼音码
                const pyArr = pinyin(val, { style: pinyin.STYLE_FIRST_LETTER })
                this.addForm.InputCode = pyArr.map(arr => arr[0]).join('').toUpperCase()
            } else {
                this.addForm.InputCode = ''
            }
            // 触发表单变化事件
            this.onFormChange()
        },

        // 修改子表格行（只填充表单，不调接口）
        handleEditRow(row) {
            this.addForm = {
                ItemClass: row.ItemClass,
                ItemCode: row.ItemCode,
                ItemName: row.ItemName,
                InputCode: row.InputCode,
                startDateTime: row.startDateTime,
                stopDateTime: this.isEdit ? '' : row.stopDateTime
            }
            this.$message.info('数据已填充到表单中，可以修改后重新添加')
        },
        stopClick(row) {
            this.stopData = row
            this.stopStatus = true;
        },
        // 停用按钮操作
        handleClose() {
            let row = this.stopData;
            row.stopDate = this.stopDate;
            DeactivateDiagnosis(row).then(res => {
                if (res && res.code === 200) {
                    this.$message.success('停用诊疗项目成功')
                    this.getList()
                    this.stopDate = '';
                    this.stopStatus = false;
                } else {
                    this.$message.error(res.msg || '停用诊疗项目失败')
                }
            })
        },

        // 序号翻页递增
        indexMethod(index) {
            let nowPage = this.searchText.pageNum; //当前第几页，根据组件取值即可
            let nowLimit = this.searchText.pageSize; //当前每页显示几条，根据组件取值即可
            return index + 1 + (nowPage - 1) * nowLimit; // 这里可以理解成一个公式
        },

        // 计算表格高度
        calculateTableHeight() {
            this.$nextTick(() => {
                // 计算可用高度：窗口高度 - 页面头部 - 分页组件 - 其他边距
                const windowHeight = window.innerHeight
                const headerHeight = 200 // 页面头部大约高度
                const paginationHeight = 80 // 分页组件高度
                const margins = 100 // 其他边距

                // 计算表格可用高度，最小400px，最大800px
                const availableHeight = windowHeight - headerHeight - paginationHeight - margins
                this.tableHeight = Math.max(400, Math.min(800, availableHeight))
            })
        }
    }
}
</script>

<style lang="scss" scoped>
// 主容器样式
.inherit-platform {
    padding: 24px;
    background-color: #f6f8fa;
    min-height: calc(100vh - 84px);
}

// 页面头部样式
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 0 8px;

    .left {
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2329;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 14px;
            color: #86909c;
        }
    }

    .right {
        .el-button {
            padding: 12px 24px;
            font-size: 14px;
            border-radius: 8px;
            background: #409eff;
            border: none;
            color: #fff;
            transition: all 0.3s;

            &:hover {
                background: #66b1ff;
                box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
            }
        }
    }
}

// 空数据样式
.empty-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    .empty-img {
        width: 80px;
        height: 80px;
        margin-bottom: 12px;
        opacity: 0.7;
    }

    .empty-text {
        color: #999;
        font-size: 16px;
    }
}

.custom-add-dialog .el-dialog {
    border-radius: 14px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
}

.custom-add-dialog .el-dialog__header {
    background: #f5f7fa;
    border-radius: 14px 14px 0 0;
    padding: 18px 24px 10px 24px;
}

.custom-add-dialog .el-dialog__body {
    padding: 24px 32px 10px 32px;
    background: #fff;
}

.custom-add-dialog .el-form-item {
    margin-bottom: 22px;
}

.custom-add-dialog .el-input__inner {
    border-radius: 7px;
    height: 38px;
    font-size: 15px;
}

.custom-add-dialog .el-select .el-input__inner {
    border-radius: 7px;
}

.custom-add-dialog .el-dialog__footer {
    padding: 12px 32px 24px 32px;
    background: #f5f7fa;
    border-radius: 0 0 14px 14px;
    text-align: right;
}

.custom-add-dialog .el-button {
    min-width: 80px;
    border-radius: 7px;
    font-size: 15px;
    margin-left: 12px;
}

// 表单区域样式
.form-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 16px;
}

.form-row .el-form-item {
    flex: 1;
    margin-bottom: 0;
}

.form-section .el-form-item {
    margin-bottom: 16px;
}

.form-section .el-input__inner,
.form-section .el-select .el-input__inner {
    border-radius: 6px;
    height: 36px;
    font-size: 14px;
}

// 表格区域样式
.table-section {
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.table-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.table-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2329;
}

.table-count {
    font-size: 14px;
    color: #86909c;
}

.table-section .el-table {
    border: none;
}

.table-section .el-table th {
    background: #f8f9fa;
    color: #1f2329;
    font-weight: 600;
}

.table-section .el-table td {
    padding: 8px 0;
}
</style>
