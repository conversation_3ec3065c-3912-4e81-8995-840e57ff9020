<template>
  <div id="app">
    <router-view />
    <theme-picker />
  </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker"
import {removeToken,removeAccessToken,removeAccessId} from '@/utils/auth'
export default {
  name: "App",
  components: { ThemePicker },
  mounted() {
    window.addEventListener('beforeunload', this.handleUnload);
  },
  beforeDestroy() {
    window.removeEventListener('beforeunload', this.handleUnload);
  },
  methods: {
    handleUnload() {
      // 清理所有可能的存储
      removeToken();
      removeAccessToken();
      removeAccessId();
    }
  }
}
</script>
<style scoped>
#app .theme-picker {
  display: none;
}
</style>
