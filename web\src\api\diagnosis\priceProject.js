import request from '@/utils/request'

export function GetPriceProjectList(itemClass, itemCode) {
  return request({
    url: '/PriceProject/GetPriceProjectList?itemClass=' + itemClass + '&itemCode=' + itemCode,
    method: 'get',
  })
}

export function UpdatePriceProject(data) {
  return request({
    url: '/PriceProject/UpdatePriceProject',
    method: 'put',
    data: data,
  })
}

export function AddPriceProject(data) {
  return request({
    url: '/PriceProject/AddPriceProject',
    method: 'post',
    data: data,
  })
}
