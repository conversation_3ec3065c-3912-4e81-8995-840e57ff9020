﻿<template>
  <div class="price-item-container">
    <!-- 查询表单 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="80px">
        <el-form-item label="项目分类">
          <el-select v-model="queryParams.itemClass" placeholder="请选择项目分类" clearable @change="handleQuery"
                     style="width: 200px">
            <el-option
              v-for="item in billItemClassDictList"
              :key="item.classCode"
              :label="item.className"
              :value="item.classCode"/>
          </el-select>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model="queryParams.itemName" placeholder="请输入项目名称" clearable @input="handleQuery"
                    style="width: 200px"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button-group>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button type="success" icon="el-icon-plus" @click="handleAdd">新增</el-button>
          </el-button-group>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table :data="PriceItemNameList" stripe border class="main-table">
        <el-table-column type="index" label="序号" align="center" width="60"/>
        <el-table-column label="项目类型" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="billItemClassDictList.find(item => item.classCode === scope.row.iteM_CLASS)"
                    type="info" effect="dark" size="mini">
              {{ billItemClassDictList.find(item => item.classCode === scope.row.iteM_CLASS).className }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column property="iteM_NAME" label="项目名称" align="center" show-overflow-tooltip/>
        <el-table-column property="iteM_CODE" label="项目代码" align="center" show-overflow-tooltip/>
        <el-table-column property="inpuT_CODE" label="输入码" align="center" show-overflow-tooltip width="120"/>
        <el-table-column property="stoP_DATE_TIME" label="停用时间" align="center" show-overflow-tooltip width="160"/>
        <el-table-column label="操作" align="center" width="280">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" icon="el-icon-edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleDelete(scope.row)">停用</el-button>
            <el-button size="mini" type="warning" icon="el-icon-document" @click="handlePriceMaintenance(scope.row)">
              价表维护
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination v-show="queryParams.total > 0"
                  :total="queryParams.total"
                  :page.sync="queryParams.pageNum"
                  :limit.sync="queryParams.pageSize"
                  @pagination="getList"/>
    </el-card>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="isEdit ? '编辑项目' : '新增项目'" :visible.sync="dialogVisible" width="50%"
               :close-on-click-modal="false">
      <el-form ref="form" :model="form" label-width="100px" :rules="rules" label-position="right">
        <el-row :gutter="30">
          <el-col :span="12">
            <el-form-item label="项目分类" prop="iteM_CLASS">
              <el-select v-model="form.iteM_CLASS" placeholder="请选择项目分类" style="width: 100%">
                <el-option
                  v-for="item in billItemClassDictList"
                  :key="item.classCode"
                  :label="item.className"
                  :value="item.classCode"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="iteM_NAME">
              <el-input v-model="form.iteM_NAME" @input="onNameInput" show-word-limit maxlength="50"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目代码" prop="iteM_CODE">
              <el-input v-model="form.iteM_CODE" show-word-limit maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="输入码">
              <el-input v-model="form.inpuT_CODE" autocomplete="off" placeholder="自动生成输入码" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" icon="el-icon-close">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" icon="el-icon-check">确 定</el-button>
      </span>
    </el-dialog>

    <!--    *********************************************-->

    <!--    // 价表详细信息弹窗-->
    <el-drawer
      title="价表详细信息"
      :visible.sync="priceDetailDrawerVisible"
      direction="rtl"
      size="83%"
      :close-on-click-modal="false"
    >
      <div style="padding: 20px;">
        <el-form :model="priceDetailForm" label-width="120px" label-position="right">
          <el-row :gutter="20">
            <!-- 基础信息 -->
            <el-col :span="6">
              <el-form-item label="项目代码">
                <el-input v-model="priceDetailForm.iteM_CODE" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="项目名称">
                <el-input v-model="priceDetailForm.iteM_NAME" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="项目规格">
                <el-input v-model="priceDetailForm.iteM_SPEC"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="单位">
                <el-input v-model="priceDetailForm.units"/>
              </el-form-item>
            </el-col>

            <!-- 价格信息 -->
            <el-col :span="6">
              <el-form-item label="价格">
                <el-input-number v-model.number="priceDetailForm.price" :precision="2" :step="0.1" style="width: 100%"
                                 disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="优惠价格">
                <el-input-number v-model.number="priceDetailForm.prefeR_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="外宾价格">
                <el-input-number v-model.number="priceDetailForm.foreigneR_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="最高限价">
                <el-input-number v-model.number="priceDetailForm.higH_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
            </el-col>

            <!-- 分类与关联 -->
            <el-col :span="6">
              <el-form-item label="住院收费类别">
                <el-select v-model="priceDetailForm.clasS_ON_INP_RCPT" placeholder="请选择住院收费类别"
                           style="width: 100%">
                  <el-option
                    v-for="item in inpRcptFeeDictList"
                    :key="item.feE_CLASS_CODE"
                    :label="item.feE_CLASS_NAME"
                    :value="item.feE_CLASS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="门诊收费类别">
                <el-select v-model="priceDetailForm.clasS_ON_OUTP_RCPT" placeholder="请选择门诊收费类别"
                           style="width: 100%">
                  <el-option
                    v-for="item in outpRcptFeeDictList"
                    :key="item.feE_CLASS_CODE"
                    :label="item.feE_CLASS_NAME"
                    :value="item.feE_CLASS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="核算项目类别">
                <el-select v-model="priceDetailForm.clasS_ON_RECKONING" placeholder="请选择核算项目类别"
                           style="width: 100%">
                  <el-option
                    v-for="item in reckItemClassDictList"
                    :key="item.clasS_CODE"
                    :label="item.clasS_NAME"
                    :value="item.clasS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="会计科目类别">
                <el-select v-model="priceDetailForm.subJ_CODE" placeholder="请选择会计科目类别" style="width: 100%">
                  <el-option
                    v-for="item in tallySubjecDictList"
                    :key="item.subJ_CODE"
                    :label="item.subJ_NAME"
                    :value="item.subJ_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 时间与状态 -->
            <el-col :span="6">
              <el-form-item label="开始日期">
                <el-date-picker
                  v-model="priceDetailForm.starT_DATE"
                  type="datetime"
                  placeholder="选择开始日期和时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="结束日期">
                <el-date-picker
                  v-model="priceDetailForm.stoP_DATE"
                  type="datetime"
                  placeholder="选择结束日期和时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="计费标记">
                <el-select v-model="priceDetailForm.chargE_FLAG" clearable placeholder="请选择" style="width: 100%">
                  <el-option label="不计费" value="0"/>
                  <el-option label="计费" value="1"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="计费范围">
                <el-select v-model="priceDetailForm.chargE_COPE" clearable placeholder="请选择" style="width: 100%">
                  <el-option label="仅门诊" value="0"/>
                  <el-option label="仅住院" value="1"/>
                  <el-option label="门诊住院均可" value="2"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="中医项目标识">
                <el-select v-model="priceDetailForm.tcM_ITEM_FLAG" clearable placeholder="请选择" style="width: 100%">
                  <el-option label="是" value="1"/>
                  <el-option label="否" value="0"/>
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 其他字段 -->
            <el-col :span="6">
              <el-form-item label="默认执行科室">
                <el-input v-model="priceDetailForm.performeD_BY"/>
              </el-form-item>
            </el-col>
            <!--            <el-col :span="6">-->
            <!--              <el-form-item label="费别屏蔽标志">-->
            <!--                <el-input v-model="priceDetailForm.feE_TYPE_MASK"/>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <el-col :span="6">
              <el-form-item label="病案费用类别">
                <el-select v-model="priceDetailForm.clasS_ON_MR" placeholder="请选择病案费用类别" style="width: 100%">
                  <el-option
                    v-for="item in mrFeeClassDictList"
                    :key="item.mR_FEE_CLASS_CODE"
                    :label="item.mR_FEE_CLASS_NAME"
                    :value="item.mR_FEE_CLASS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="备注">
                <el-input v-model="priceDetailForm.memo" show-word-limit maxlength="200"/>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="物价代码">
                <el-input v-model="priceDetailForm.materiaL_CODE"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="输入码">
                <el-input v-model="priceDetailForm.inpuT_CODE" disabled/>
              </el-form-item>
            </el-col>
            <!--            <el-col :span="6">-->
            <!--              <el-form-item label="病案费用类别新">-->
            <!--                <el-input v-model="priceDetailForm.clasS_ON_MR_NEW"/>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <!--            <el-col :span="6">-->
            <!--              <el-form-item label="病案费用类别旧">-->
            <!--                <el-input v-model="priceDetailForm.clasS_ON_MR_OLD">-->
            <!--                </el-input>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->

            <el-col :span="6">
              <el-form-item label="医保费用上传">
                <el-input v-model="priceDetailForm.iteM_CODE_MIU"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="项目内涵">
                <el-input v-model="priceDetailForm.connotation" type="textarea" :rows="2"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="除外内容">
                <el-input v-model="priceDetailForm.exclusions" type="textarea" :rows="2"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="说明">
                <el-input v-model="priceDetailForm.explain" type="textarea" :rows="2"/>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="医保国码">
                <el-input v-model="priceDetailForm.natioN_CODE"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="医保国码名称">
                <el-input v-model="priceDetailForm.natioN_NAME"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div style="text-align: right; margin-top: 15px;">
          <el-button type="warning" @click="updatePrice">修改价格</el-button>
          <el-button type="danger" @click="priceDetailDrawerVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPriceDetail">保存</el-button>
        </div>
      </div>
    </el-drawer>

    <!--    // 新增行价表详细信息-->
    <el-drawer
      title="新增价表详细信息"
      :visible.sync="AddPriceDetailDrawerVisible"
      direction="rtl"
      size="83%"
      :close-on-click-modal="false"
    >
      <div style="padding: 20px;">
        <el-form ref="addPriceDetailFormRef" :model="addPpriceDetailForm" label-width="120px" label-position="right">
          <el-row :gutter="20">
            <!-- 基础信息 -->
            <el-col :span="6">
              <el-form-item label="项目代码">
                <el-input v-model="addPpriceDetailForm.iteM_CODE" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="项目名称">
                <el-input v-model="addPpriceDetailForm.iteM_NAME" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="项目规格">
                <el-input v-model="addPpriceDetailForm.iteM_SPEC"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="单位">
                <el-input v-model="addPpriceDetailForm.units"/>
              </el-form-item>
            </el-col>

            <!-- 价格信息 -->
            <el-col :span="6">
              <el-form-item label="价格">
                <el-input-number v-model.number="addPpriceDetailForm.price" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="优惠价格">
                <el-input-number v-model.number="addPpriceDetailForm.prefeR_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="外宾价格">
                <el-input-number v-model.number="addPpriceDetailForm.foreigneR_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="最高限价">
                <el-input-number v-model.number="addPpriceDetailForm.higH_PRICE" :precision="2" :step="0.1"
                                 style="width: 100%"/>
              </el-form-item>
            </el-col>

            <!-- 分类与关联 -->
            <el-col :span="6">
              <el-form-item label="住院收费类别">
                <el-select v-model="addPpriceDetailForm.clasS_ON_INP_RCPT" placeholder="请选择住院收费类别"
                           style="width: 100%">
                  <el-option
                    v-for="item in inpRcptFeeDictList"
                    :key="item.feE_CLASS_CODE"
                    :label="item.feE_CLASS_NAME"
                    :value="item.feE_CLASS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="门诊收费类别">
                <el-select v-model="addPpriceDetailForm.clasS_ON_OUTP_RCPT" placeholder="请选择门诊收费类别"
                           style="width: 100%">
                  <el-option
                    v-for="item in outpRcptFeeDictList"
                    :key="item.feE_CLASS_CODE"
                    :label="item.feE_CLASS_NAME"
                    :value="item.feE_CLASS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="核算项目类别">
                <el-select v-model="addPpriceDetailForm.clasS_ON_RECKONING" placeholder="请选择核算项目类别"
                           style="width: 100%">
                  <el-option
                    v-for="item in reckItemClassDictList"
                    :key="item.clasS_CODE"
                    :label="item.clasS_NAME"
                    :value="item.clasS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="会计科目类别">
                <el-select v-model="addPpriceDetailForm.subJ_CODE" placeholder="请选择会计科目类别" style="width: 100%">
                  <el-option
                    v-for="item in tallySubjecDictList"
                    :key="item.subJ_CODE"
                    :label="item.subJ_NAME"
                    :value="item.subJ_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 时间与状态 -->
            <el-col :span="6">
              <el-form-item label="开始日期" prop="starT_DATE"
                            :rules="[{ required: true, message: '请选择开始日期和时间', trigger: 'change' }]">
                <el-date-picker
                  v-model="addPpriceDetailForm.starT_DATE"
                  type="datetime"
                  placeholder="选择开始日期和时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="结束日期">
                <el-date-picker
                  v-model="addPpriceDetailForm.stoP_DATE"
                  type="datetime"
                  placeholder="选择结束日期和时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="计费标记">
                <el-select v-model="addPpriceDetailForm.chargE_FLAG" clearable placeholder="请选择" style="width: 100%">
                  <el-option label="不计费" value="0"/>
                  <el-option label="计费" value="1"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="计费范围">
                <el-select v-model="addPpriceDetailForm.chargE_COPE" clearable placeholder="请选择" style="width: 100%">
                  <el-option label="仅门诊" value="0"/>
                  <el-option label="仅住院" value="1"/>
                  <el-option label="门诊住院均可" value="2"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="中医项目标识">
                <el-select v-model="addPpriceDetailForm.tcM_ITEM_FLAG" clearable placeholder="请选择" style="width: 100%">
                  <el-option label="是" value="1"/>
                  <el-option label="否" value="0"/>
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 其他字段 -->
            <el-col :span="6">
              <el-form-item label="默认执行科室">
                <el-input v-model="addPpriceDetailForm.performeD_BY"/>
              </el-form-item>
            </el-col>
            <!--            <el-col :span="6">-->
            <!--              <el-form-item label="费别屏蔽标志">-->
            <!--                <el-input v-model="addPpriceDetailForm.feE_TYPE_MASK"/>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <el-col :span="6">
              <el-form-item label="病案费用类别">
                <el-select v-model="addPpriceDetailForm.clasS_ON_MR" placeholder="请选择病案费用类别" style="width: 100%">
                  <el-option
                    v-for="item in mrFeeClassDictList"
                    :key="item.mR_FEE_CLASS_CODE"
                    :label="item.mR_FEE_CLASS_NAME"
                    :value="item.mR_FEE_CLASS_CODE"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="备注">
                <el-input v-model="addPpriceDetailForm.memo" show-word-limit maxlength="200"/>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="物价代码">
                <el-input v-model="addPpriceDetailForm.materiaL_CODE"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="输入码">
                <el-input v-model="addPpriceDetailForm.inpuT_CODE" disabled/>
              </el-form-item>
            </el-col>
            <!--            <el-col :span="6">-->
            <!--              <el-form-item label="病案费用类别新">-->
            <!--                <el-input v-model="addPpriceDetailForm.clasS_ON_MR_NEW"/>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <!--            <el-col :span="6">-->
            <!--              <el-form-item label="病案费用类别旧">-->
            <!--                <el-input v-model="addPpriceDetailForm.clasS_ON_MR_OLD"/>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->

            <el-col :span="6">
              <el-form-item label="医保费用上传">
                <el-input v-model="addPpriceDetailForm.iteM_CODE_MIU"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="项目内涵">
                <el-input v-model="addPpriceDetailForm.connotation" type="textarea" :rows="2"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="除外内容">
                <el-input v-model="addPpriceDetailForm.exclusions" type="textarea" :rows="2"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="说明">
                <el-input v-model="addPpriceDetailForm.explain" type="textarea" :rows="2"/>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="医保国码">
                <el-input v-model="addPpriceDetailForm.natioN_CODE"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="医保国码名称">
                <el-input v-model="addPpriceDetailForm.natioN_NAME"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div style="text-align: right; margin-top: 15px;">
          <el-button @click="AddPriceDetailDrawerVisible = false">取消</el-button>
          <el-button type="primary" @click="addSubmitPriceDetail">保存</el-button>
        </div>
      </div>
    </el-drawer>


    <!-- 原有价表维护弹窗 -->
    <el-dialog title="价表维护" :visible.sync="priceMaintenanceDialogVisible" width="88%"
               :close-on-click-modal="false">
      <el-form :model="priceMaintenanceForm" label-width="100px" label-position="right">
        <el-form-item label="项目名称">
          <el-input v-model="priceMaintenanceForm.iteM_NAME" disabled/>
        </el-form-item>
        <el-form-item label="项目代码">
          <el-input v-model="priceMaintenanceForm.iteM_CODE" disabled/>
        </el-form-item>

        <!-- 维护列表 -->
        <el-table :data="priceMaintenanceList" stripe border class="main-table">
          <el-table-column label="项目类别" align="center">
            <template slot-scope="scope">
              <el-tag v-if="billItemClassDictList.find(item => item.classCode === scope.row.iteM_CLASS)"
                      type="info" effect="dark" size="mini">
                {{ billItemClassDictList.find(item => item.classCode === scope.row.iteM_CLASS).className }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column property="iteM_SPEC" label="项目规格" align="center" show-overflow-tooltip/>
          <el-table-column property="units" label="单位" align="center" show-overflow-tooltip/>
          <el-table-column property="price" label="价格" align="center" show-overflow-tooltip/>
          <el-table-column property="starT_DATE" label="开始日期" align="center" show-overflow-tooltip/>
          <el-table-column label="操作" align="center" width="180">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" icon="el-icon-edit"
                         @click="openPriceDetailDrawer(scope.$index, scope.row)">编辑
              </el-button>
<!--              <el-button size="mini" type="danger" icon="el-icon-delete" @click="removePriceRow(scope.$index)">删除-->
<!--              </el-button>-->
            </template>
          </el-table-column>
        </el-table>

        <div style="margin-top: 10px; text-align: right">
          <el-button icon="el-icon-plus" @click="addPriceRow">新增行</el-button>
        </div>

      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  GetBillItemClassDict,
  GetPriceItemNameDict,
  AddPriceItem,
  UpdatePriceItem,
  DeletePriceItem,
  GetPriceList,
  GetPriceListByItemCodeItemClass,
  GetClassTypeList,
  UpdatePriceList,
  AddAllPriceList
} from "../../api/InspectionItems/PriceItem";
import pinyin from "pinyin";

export default {
  name: 'PriceItem',
  props: [],
  components: {},
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        itemClass: '',
        itemName: ''
      },
      // 价格项
      PriceItemNameList: [],
      // 账单项目分类
      billItemClassDictList: [],
      // 对话框相关
      dialogVisible: false,
      isEdit: false,
      form: {
        id: null,
        iteM_CLASS: '',
        iteM_NAME: '',
        iteM_CODE: '',
        inpuT_CODE: '',
        tempItemName: '',
        tempItemCode: ''
      },
      tempItemName: '',
      tempItemCode: '',
      rules: {
        iteM_CLASS: [
          {required: true, message: '请选择项目分类', trigger: 'change'}
        ],
        iteM_NAME: [
          {required: true, message: '请输入项目名称', trigger: 'blur'},
          {min: 2, max: 50, message: '项目名称长度在2到50个字符之间', trigger: 'blur'}
        ],
        iteM_CODE: [
          {required: true, message: '请输入项目代码', trigger: 'blur'},
          {min: 2, max: 20, message: '项目代码长度在2到20个字符之间', trigger: 'blur'},
          {pattern: /^[A-Za-z0-9]+$/, message: '项目代码只能包含字母和数字', trigger: 'blur'}
        ]
      },
      // 价表维护相关
      priceMaintenanceDialogVisible: false,
      priceMaintenanceForm: {
        iteM_NAME: '',
        iteM_CODE: '',
        itemClass: ''
      },
      priceMaintenanceList: [],
      editingIndex: -1, // 记录正在编辑的行索引
      priceDetailDrawerVisible: false, // 控制抽屉显示
      priceDetailForm: { // 抽屉表单数据
      },
      inpRcptFeeDictList: [],// 住院收费
      mrFeeClassDictList: [],// 病案费用
      outpRcptFeeDictList: [],// 门诊收费
      reckItemClassDictList: [],//核算项目
      tallySubjecDictList: [],//会计科目,
      //价表编辑主键条件
      tempEditItemCode: '',
      tempEditItemSpec: '',
      tempEditItemName: '',
      tempEditUnits: '',
      tempEditStartDate: '',
      addPpriceDetailForm: {},
      AddPriceDetailDrawerVisible: false,
      inpuT_CODE: '',
      itemClass: '',
    }
  },
  created() {
    this.getDict()
    this.classType()
  },
  mounted() {
  },
  methods: {
    getDict() {
      GetBillItemClassDict().then(res => {
        this.billItemClassDictList = res.data
        this.getList()
      })
    },
    classType() {
      GetClassTypeList().then(res => {
        this.inpRcptFeeDictList = res.data.inpRcptFeeDictRows
        this.mrFeeClassDictList = res.data.mrFeeClassDictRows
        this.outpRcptFeeDictList = res.data.outpRcptFeeDictRows
        this.reckItemClassDictList = res.data.reckItemClassDictRows
        this.tallySubjecDictList = res.data.tallySubjecDictRows
      })
    },
    getList() {
      GetPriceItemNameDict(this.queryParams).then(res => {
        this.PriceItemNameList = res.data.rows
        this.queryParams.total = res.data.total
      })
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleAdd() {
      this.isEdit = false;
      this.form = {
        id: null,
        iteM_CLASS: '',
        iteM_NAME: '',
        iteM_CODE: '',
        inpuT_CODE: ''
      };
      this.dialogVisible = true;
    },
    handleEdit(row) {
      this.tempItemName = row.iteM_NAME;
      this.tempItemCode = row.iteM_CODE;
      this.isEdit = true;
      this.form = {...row};
      this.dialogVisible = true;
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.isEdit) {
            // 更新操作
            this.form.tempItemName = this.tempItemName;
            this.form.tempItemCode = this.tempItemCode;
            UpdatePriceItem(this.form).then(() => {
              // this.$message.success('更新成功');
              this.dialogVisible = false;
              this.getList();
            }).catch(error => {
              console.error('更新失败:', error);
              this.$message.error('更新失败');
            });
          } else {
            // 新增操作
            AddPriceItem(this.form).then(() => {
              this.$message.success('新增成功');
              this.dialogVisible = false;
              this.getList();
            }).catch(error => {
              console.error('新增失败:', error);
              this.$message.error('新增失败');
            });
          }
        } else {
          this.$message.error('请完善表单信息');
          return false;
        }
      });
    },
    // 项目名称输入自动生成输入码
    onNameInput(val) {
      if (val) {
        // 取首字母大写输入码
        const pyArr = pinyin(val, {style: pinyin.STYLE_FIRST_LETTER})
        this.form.inpuT_CODE = pyArr.map(arr => arr[0]).join('').toUpperCase()
      } else {
        this.form.inpuT_CODE = ''
      }
    },
    // 停用数据
    handleDelete(row) {
      const temp = {
        iteM_NAME: row.iteM_NAME,
        iteM_CODE: row.iteM_CODE,
      };
      this.$confirm('是否确认停用该数据项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DeletePriceItem(temp).then(() => {
          this.$message.success('停用成功');
          this.getList();
        }).catch(error => {
          console.error('停用失败：', error);
          this.$message.error('停用失败');
        });
      }).catch(() => {
        this.$message.info('已取消停用');
      });
    },
    // 价表维护
    handlePriceMaintenance(row) {
      this.priceMaintenanceForm = {
        iteM_NAME: row.iteM_NAME,
        iteM_CODE: row.iteM_CODE,
        itemClass: row.iteM_CLASS
      };

      GetPriceList(this.priceMaintenanceForm).then(res => {
        if (res.data.rows && res.data.rows.length > 0) {
          this.inpuT_CODE = res.data.rows[0].inpuT_CODE
          this.itemClass = res.data.rows[0].iteM_CLASS
        }
        this.priceMaintenanceList = res.data.rows || [];
      });

      this.priceMaintenanceDialogVisible = true;
    },
    // 打开抽屉并加载当前行数据
    openPriceDetailDrawer(index, row) {
      this.editingIndex = index;
      const temp = {...row}; // 复制当前行数据到抽屉表单
      //添加编辑查询接口
      GetPriceListByItemCodeItemClass(temp).then(res => {
        this.priceDetailForm = res.data;
        this.tempEditItemCode = res.data.iteM_CODE
        this.tempEditItemName = res.data.iteM_NAME
        this.tempEditItemSpec = res.data.iteM_SPEC
        this.tempEditUnits = res.data.units
        this.tempEditStartDate = res.data.starT_DATE
      })
      this.priceDetailDrawerVisible = true;
    },
    // 提交抽屉数据
    submitPriceDetail() {
      // 将修改后的数据同步回主表
      this.priceDetailForm.tempEditItemCode = this.tempEditItemCode
      this.priceDetailForm.tempEditItemName = this.tempEditItemName
      this.priceDetailForm.tempEditItemSpec = this.tempEditItemSpec
      this.priceDetailForm.tempEditUnits = this.tempEditUnits
      this.priceDetailForm.tempEditStartDate = this.tempEditStartDate
      //保存到数据库
      UpdatePriceList(this.priceDetailForm).then(() => {
        // this.$message.success('数据已更新');
        const query = {
          iteM_CODE: this.priceDetailForm.iteM_CODE,
          iteM_NAME: this.priceDetailForm.iteM_NAME,
          itemClass: this.priceDetailForm.iteM_CLASS
        };
        GetPriceList(query).then(res => {
          this.priceMaintenanceList = res.data.rows || [];
        });
        this.getList(); // 刷新主列表以显示最新数据
      }).catch(error => {
        console.error('数据更新失败:', error);
        this.$message.error('数据更新失败');
      });
      this.priceDetailDrawerVisible = false;
    },

    addSubmitPriceDetail() {
      this.$refs.addPriceDetailFormRef.validate(valid => {
        if (valid) {
          if (!this.addPpriceDetailForm.iteM_CLASS) {
            this.addPpriceDetailForm.iteM_CLASS = this.itemClass
          }
          //保存到数据库
          AddAllPriceList(this.addPpriceDetailForm).then(() => {
            const query = {
                iteM_CODE: this.addPpriceDetailForm.iteM_CODE,
                iteM_NAME: this.addPpriceDetailForm.iteM_NAME,
                itemClass: this.addPpriceDetailForm.iteM_CLASS
              }
            ;
            GetPriceList(query).then(res => {
              this.priceMaintenanceList = res.data.rows || [];
            });
            this.getList(); // 刷新主列表以显示最新数据
          }).catch(error => {
            console.error('数据更新失败:', error);
            this.$message.error('数据更新失败');
          });
          this.AddPriceDetailDrawerVisible = false;
          this.priceMaintenanceDialogVisible = true
        } else {
          this.$message.error('请完善表单信息');
          return false;
        }
      });

    },
    // // 移除价表行
    // removePriceRow(index) {
    //   this.priceMaintenanceList.splice(index, 1);
    // },
    //新增行
    addPriceRow() {
      this.addPpriceDetailForm = {
        iteM_CODE: this.priceMaintenanceForm.iteM_CODE,
        iteM_NAME: this.priceMaintenanceForm.iteM_NAME,
        iteM_CLASS: this.priceMaintenanceForm.itemClass,
        starT_DATE: "",
        units: "",
        iteM_SPEC: "",
        price: 0.00,
        prefeR_PRICE: 0.00,
        foreigneR_PRICE: 0.00,
        higH_PRICE: 0.00,
        clasS_ON_INP_RCPT: "",
        clasS_ON_OUTP_RCPT: "",
        clasS_ON_RECKONING: "",
        subJ_CODE: "",
        stoP_DATE: "",
        chargE_FLAG: "",
        chargE_COPE: "",
        tcM_ITEM_FLAG: "",
        performeD_BY: "",
        feE_TYPE_MASK: "",
        clasS_ON_MR: "",
        memo: "",
        materiaL_CODE: "",
        inpuT_CODE: this.inpuT_CODE,
        clasS_ON_MR_NEW: "",
        clasS_ON_MR_OLD: "",
        iteM_CODE_MIU: "",
        connotation: "",
        exclusions: "",
        explain: "",
        natioN_CODE: "",
        natioN_NAME: ""
      };
      this.AddPriceDetailDrawerVisible = true;
    },
    //修改价格
    updatePrice() {
      this.addPpriceDetailForm = {
        iteM_CODE: this.priceDetailForm.iteM_CODE,
        iteM_NAME: this.priceDetailForm.iteM_NAME,
        iteM_CLASS: this.priceDetailForm.itemClass,
        starT_DATE: "",
        units: this.priceDetailForm.units,
        iteM_SPEC: this.priceDetailForm.iteM_SPEC,
        price: 0.00,
        prefeR_PRICE: 0.00,
        foreigneR_PRICE: 0.00,
        higH_PRICE: this.priceDetailForm.higH_PRICE,
        clasS_ON_INP_RCPT: this.priceDetailForm.clasS_ON_INP_RCPT,
        clasS_ON_OUTP_RCPT: this.priceDetailForm.clasS_ON_OUTP_RCPT,
        clasS_ON_RECKONING: this.priceDetailForm.clasS_ON_RECKONING,
        subJ_CODE: this.priceDetailForm.subJ_CODE,
        stoP_DATE: "",
        chargE_FLAG: this.priceDetailForm.chargE_FLAG,
        chargE_COPE: this.priceDetailForm.chargE_COPE,
        tcM_ITEM_FLAG: this.priceDetailForm.tcM_ITEM_FLAG,
        performeD_BY: this.priceDetailForm.performeD_BY,
        feE_TYPE_MASK: this.priceDetailForm.feE_TYPE_MASK,
        clasS_ON_MR: this.priceDetailForm.clasS_ON_MR,
        memo: this.priceDetailForm.memo,
        materiaL_CODE: this.priceDetailForm.materiaL_CODE,
        inpuT_CODE: this.inpuT_CODE,
        clasS_ON_MR_NEW: this.priceDetailForm.clasS_ON_MR_NEW,
        clasS_ON_MR_OLD: this.priceDetailForm.clasS_ON_MR_OLD,
        iteM_CODE_MIU: this.priceDetailForm.iteM_CODE_MIU,
        connotation: this.priceDetailForm.connotation,
        exclusions: this.priceDetailForm.exclusions,
        explain: this.priceDetailForm.explain,
        natioN_CODE: this.priceDetailForm.natioN_CODE,
        natioN_NAME: this.priceDetailForm.natioN_NAME
      };
      this.AddPriceDetailDrawerVisible = true;
      this.priceDetailDrawerVisible = false;
    }
  },
}
</script>

<style scoped>
.price-item-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.main-table {
  width: 100%;
}

.pagination-container {
  text-align: right;
  margin-top: 15px;
}

.el-table-column--selection .cell {
  text-align: center;
}

.el-form-item__content {
  line-height: 30px;
}

.el-input-number.full-width {
  width: 100%;
}

::v-deep .el-dialog__body {
  padding: 20px 20px 0 20px;
}

.dialog-footer {
  text-align: right;
  padding: 10px 20px 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: bold;
  color: #333;
}
</style>
