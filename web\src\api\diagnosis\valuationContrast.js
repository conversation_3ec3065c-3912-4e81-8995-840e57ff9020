import request from '@/utils/request'

export function GetValuationDetailByProjectCode(itemCode,itemClass,type) {
  return request({
    url: '/ValuationContrast/GetValuationDetailByProjectCode?itemCode=' + itemCode + '&itemClass=' + itemClass + '&type=' + type,
    method: 'get'
  })
}


export function GetValuationDetailList(itemClass) {
  return request({
    url: '/ValuationContrast/GetValuationDetailList?itemClass=' + itemClass,
    method: 'get'
  })
}

export function AddValuationDetailList(data) {
  return request({
    url: '/ValuationContrast/AddValuationDetailList',
    method: 'post',
    data: data
  })
}

export function StopValuationProject(data) {
  return request({
    url: '/ValuationContrast/StopValuationProject',
    method: 'put',
    data: data
  })
}

export function UpdateValuationAmount(data) {
  return request({
    url: '/ValuationContrast/UpdateValuationAmount',
    method: 'put',
    data: data
  })
}

export function DeleteValuation(data) {
  return request({
    url: '/ValuationContrast/DeleteValuation',
    method: 'put',
    data: data
  })
}
