/*
 * FilePath     : \src\utils\ApiConfig.js
 * Author       : LYF
 * Date         : 2024-06-11 16:52
 * LastEditors  : LYF
 * LastEditTime : 2024-08-05 10:05
 * Description  :
 * CodeIterationRecord:
 */
const env = {
  api_url: "http://*********:14387/api",
  mappings: [
    { ip: "localhost", url: "http://localhost:64911/api" },
    { ip: "*********", url: "http://*********:6099/api" },
    { ip: "*************", url: "http://*************:6099/api" },
    { ip: "**********", url: "http://*********:1001/api" },
    { ip: "************", url: "http://*************:10001/api" },

    { ip: "*********", url: "http://*********:1002/api" },
    { ip: "************", url: "http://************:1002/api" },
    { ip: "*********", url: "http://*********:1002/api" },
    { ip: "*************", url: "http://*************:1002/api" },
  ],
  baseMapps: [
    { ip: "localhost", url: "http://localhost:14387" },
    { ip: "*********", url: "http://*********:6099" },
    { ip: "*************", url: "http://*************:6099" },
    { ip: "**********", url: "http://*********:1001/" },
    { ip: "************", url: "http://*************:1001/" },
  ],
  baseMappsTwo: [
    { ip: "localhost", url: "http://*********:14387/" },
    { ip: "*********", url: "http://*********:6099/" },
    { ip: "*************", url: "http://*************:6099" },
    { ip: "**********", url: "http://*********:10001/" },
  ],
  baseMappsFive: [{ ip: "**********", url: "http://**********:8004/" }],
  baseMappsThree: [
    { ip: "localhost", url: "http://**********:8004/" },
    { ip: "*********", url: "http://**********:8004/" },
    { ip: "**********", url: "http://**********:8004/" },
    { ip: "*************", url: "http://*************:8004" },
    { ip: "*************", url: "http://*************:8004" },
  ],
  get_api_url() {
    let ip = window.location.hostname;
    let mapping = this.mappings.find((t) => {
      return t.ip === ip;
    });

    if (mapping !== undefined) {
      return mapping.url;
    }
    return this.api_url;
  },

  get_api_urlTwo() {
    let ip = window.location.hostname;
    let baseMappsTwo = this.baseMappsTwo.find((t) => {
      return t.ip === ip;
    });

    if (baseMappsTwo !== undefined) {
      return baseMappsTwo.url;
    }
    return this.api_url;
  },

  get_api_urlFive() {
    let ip = window.location.hostname;
    let baseMappsFive = this.baseMappsFive.find((t) => {
      return t.ip === ip;
    });

    if (baseMappsFive !== undefined) {
      return baseMappsFive.url;
    }
    return this.api_url;
  },

  get_api_urlThree() {
    let ip = window.location.hostname;
    let baseMappsThree = this.baseMappsThree.find((t) => {
      return t.ip === ip;
    });

    if (baseMappsThree !== undefined) {
      return baseMappsThree.url;
    }
    return this.api_url;
  },

  /**
   * 判断当前请求地址是内网还是外网
   * true代表是内网 false是外网
   */
  getIsExternalRequest() {
    let ip = window.location.hostname;
    if (ip.indexOf("192.168") > -1) {
      return true;
    } else {
      return false;
    }
  },

  base_url: "http://localhost:14387/",
  get_base() {
    let ip = window.location.hostname;
    let baseMapping = this.baseMapps.find((t) => {
      return t.ip === ip;
    });
    if (baseMapping !== undefined) {
      return baseMapping.url;
    }
    return this.base;
  },
  get_base_url() {
    let ip = window.location.hostname;
    let baseMapping = this.baseMapps.find((t) => {
      return t.ip === ip;
    });
    if (baseMapping !== undefined) {
      return baseMapping.url;
    }
    return this.base_url;
  },
};
export default env;
