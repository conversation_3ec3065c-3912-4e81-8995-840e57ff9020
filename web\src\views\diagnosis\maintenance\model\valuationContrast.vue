<template>
  <div class="va-contrast-home">
    <el-drawer title="计价项目维护" :visible.sync="status" :direction="direction" size="80%">
      <div class="va-master">
        <div class="va-form">
          <div class="form-item">
            <div class="form-title">
              项目编码：
            </div>
            <div class="form-text" style="color: #3A71A8">
              {{ formData.itemCode }}
            </div>
          </div>
          <div class="form-item">
            <div class="form-title">
              项目名称：
            </div>
            <div class="form-text" style="color: #3A71A8">
              {{ formData.itemName }}
            </div>
          </div>
          <div class="form-item">
            <div class="form-title">
              总金额：
            </div>
            <div class="form-text" style="color: red;font-size: 26px;">
              {{ formData.amount }}
            </div>
          </div>
        </div>
        <div style="display: flex;justify-content: center;align-items: center;margin-top: 20px;">
          <el-radio-group v-model="type" @change="getValuationDetailByProjectCode">
            <el-radio :label="'1'">未停用</el-radio>
            <el-radio :label="'2'">已停用</el-radio>
          </el-radio-group>
        </div>
        <el-alert
          title="系统提示"
          type="info"
          description="以下列表中：数量、启用时间、停用时间，都可点击进行修改。添加右上方计价项目后，点击项目名称可快速进入选择字典页面。"
          show-icon>
        </el-alert>
        <div class="va-item">
          <div class="va-table">
            <div style="display: flex;justify-content: space-between;">
              <div class="va-title">
                计价明细
              </div>
              <div style="display: flex;align-items: flex-end;">
                <el-button style="width: 150px;" size="mini" type="success" icon="el-icon-plus" @click="addValuation">
                  添加计价项目
                </el-button>
              </div>
            </div>

            <div class="table-item">
              <el-table :data="tableData" border height="550" style="width: 100%" @row-click="handleRowDblClick">
                <el-table-column prop="chargeItemClass" align="center" label="操作" width="65">
                  <template slot-scope="scope">
                    <el-button type="text" v-if="scope.row.status" @click="deleteProject(scope.row)">删除</el-button>
                    <el-button type="text" v-else @click="clearProject(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
                <el-table-column prop="chargeItemClass" align="center" label="类别" width="120">
                  <template slot-scope="scope">
                    <el-select v-if="scope.row.status" disabled v-model="scope.row.chargeItemClass"
                               placeholder="请选择">
                      <el-option
                        v-for="item in dataItem"
                        :key="item.itemCode"
                        :label="item.itemName"
                        :value="item.itemCode">
                      </el-option>
                    </el-select>
                    <el-select v-else v-model="scope.row.chargeItemClass" placeholder="请选择">
                      <el-option
                        v-for="item in dataItem"
                        :key="item.itemCode"
                        :label="item.itemName"
                        :value="item.itemCode">
                      </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="chargeItemNo" align="center" label="编号" width="60"/>
                <el-table-column prop="chargeItemCode" align="center" label="编码"/>
                <el-table-column prop="chargeItemName" align="center" label="项目名称">
                  <template slot-scope="scope">
                    <el-tooltip v-if="!scope.row.status" class="item" effect="dark" content="点击此处进行字典检索" placement="top">
                      <span>{{ scope.row.chargeItemName }}</span>
                    </el-tooltip>
                    <span v-else>{{ scope.row.chargeItemName }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="amount" align="center" label="数量" width="70">
                  <template slot-scope="scope">
                    <el-input
                      v-if="scope.row.amountInputStatus"
                      v-model="scope.row.amount"
                      ref="amountRefs"
                      @blur="amountInputBlur(scope.row)"
                    >
                    </el-input>
                    <el-tooltip v-else  class="item" effect="dark" content="点击修改" placement="top">
                      <span>{{ scope.row.amount }}</span>
                    </el-tooltip>

                  </template>
                </el-table-column>
                <el-table-column prop="chargeItemSpec" align="center" label="规格"/>
                <el-table-column prop="units" align="center" label="单位"/>
                <el-table-column prop="price" align="center" label="单价" width="80"/>
                <el-table-column prop="startDateTime" align="center" label="启用时间" width="160">
                  <template slot-scope="scope">
                    <el-date-picker v-if="scope.row.startStatus"
                      v-model="scope.row.startDateTime"
                      type="datetime"
                      placeholder="选择日期时间"
                                    ref="startDateTimeRefs"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                      @blur="amountInputBlur(scope.row)">
                    </el-date-picker>
                    <el-tooltip v-else  class="item" effect="dark" content="点击修改" placement="top">
                      <span>{{ scope.row.startDateTime }}</span>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="stopDateTime" align="center" label="停用时间" width="200">
                  <template slot-scope="scope">
                    <el-date-picker v-if="scope.row.stopStatus"
                                    v-model="scope.row.stopDateTime"
                                    type="datetime"
                                    placeholder="选择日期时间"
                                    ref="stopDateTimeRefs"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    @blur="amountInputBlur(scope.row)">
                    </el-date-picker>
                    <el-tooltip v-else  class="item" effect="dark" content="点击修改" placement="top">
                      <span>{{ scope.row.stopDateTime }}</span>
                    </el-tooltip>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
        <div style="display: flex;align-items: center;justify-content: center;">
          <el-button type="success" @click="projectConfig">确定/保存</el-button>
          <el-button type="danger" @click="status = false">关闭</el-button>
        </div>
      </div>
    </el-drawer>

    <el-dialog title="计价项目选取" :visible.sync="projectStatus" width="70%">
      <div class="dialog-master">
        <el-input v-model="valuationItemSelect" placeholder="请输入关键字、拼音等进行筛选"
                  @input="valuationItemInputFilter"></el-input>
        <div class="project-table-master">
          <div class="table-master" style="width: 90%">
            <el-table :data="valuationItemDictNew" style="width: 100%" border max-height="480px"
                      @row-click="valuationItemTableClick" highlight-current-row empty-text="请输入关键字进行检索"
            >
              <el-table-column type="index" align="center" label="序号" width="50"></el-table-column>
              <el-table-column prop="itemCode" align="center" label="项目代码" width="180"></el-table-column>
              <el-table-column prop="itemName" align="center" label="项目名称"></el-table-column>
              <el-table-column prop="itemSpec" align="center" label="规格" width="160"></el-table-column>
              <el-table-column prop="units" align="center" label="单位" width="120"></el-table-column>
              <el-table-column prop="price" align="center" label="价格" width="80"></el-table-column>
            </el-table>
          </div>
          <div class="button-master">
            <el-button type="success" @click="valuationItemConfirm">确定</el-button>
            <el-button type="danger" @click="valuationItemClose">关闭</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="请选择项目停用时间" :visible.sync="stopStatus" width="30%">
      <div style="margin-top: 10px;margin-bottom: 10px;text-align: center">
        <el-date-picker v-model="stopDate" type="datetime" placeholder="选择日期时间">
        </el-date-picker>
      </div>
      <div style="text-align: center; margin: 3px">
        <el-button @click="stopStatus = false">取消</el-button>
        <el-button type="primary" @click="stopProject">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  GetValuationDetailByProjectCode,
  GetValuationDetailList,
  AddValuationDetailList,
  StopValuationProject,
  UpdateValuationAmount,
  DeleteValuation
} from '@/api/diagnosis/valuationContrast'

export default {
  name: 'valuationContrast',
  props: [],
  components: {},
  data() {
    return {
      stopDate: '',
      stopStatus: false,
      stopData: {},
      status: false,
      direction: 'rtl',
      formData: {},
      type: '1',
      tableData: [],
      dataItem: [],
      noMax: 0,
      itemRowIndex: '',
      projectStatus: false,
      projectRowData: {},
      valuationItemSelect: '',
      valuationItemDictNew: [],
      valuationItemDict: [],
      valuationItemData: [],
      valuationItemRow: {},
      priceDict:[],
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    updateProject(row) {
      row.clinicItemCode = this.formData.itemCode
      row.clinicItemClass = this.formData.itemClass
      UpdateValuationAmount(row).then(res => {
        if (res.code === 200) {
          this.$message.success(res.message)
          this.getValuationDetailByProjectCode();
        }
      })
    },
    projectConfig() {
      const loading = this.$loading({
        lock: true,
        text: '数据正在努力保存中,请耐心等待!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      let data = {
        items: this.tableData,
        forms: {
          itemClass: this.formData.itemClass,
          itemCode: this.formData.itemCode,
        },
      }
      AddValuationDetailList(data).then(res => {
        if (res.code === 200) {
          this.$message.success(res.message)
          this.getValuationDetailByProjectCode();
        }
      }).finally(() => {
        loading.close();
      })
    },
    clearProject(row) {
      let itemRowIndex = this.tableData.indexOf(row)
      this.tableData.splice(itemRowIndex, 1)
      let max = 0;
      this.tableData.forEach(t => {
        t.chargeItemNo = ++max;
      })
      this.noMax = max;
    },
    deleteProject(row) {
      row.clinicItemCode = this.formData.itemCode
      row.clinicItemClass = this.formData.itemClass
      DeleteValuation(row).then(res => {
        if (res.code === 200){
          this.$message.success(res.message)
          this.getValuationDetailByProjectCode();
        }
      })
    },
    stopProject() {
      let data = {
        itemCode: this.formData.itemCode,
        itemClass: this.formData.itemClass,
        chargeItemCode: this.stopData.chargeItemCode,
        stopDate: this.stopDate,
      }
      StopValuationProject(data).then(res => {
        if (res.code === 200) {
          this.$message.success(res.message)
          this.getValuationDetailByProjectCode(this.formData.itemCode, this.formData.itemClass)
          this.stopDate = '';
          this.stopStatus = false;
        }
      })
    },
    amountInputBlur(row) {
      this.updateProject(row);
      this.projectResetImputedPrice();
      row.amountInputStatus = false;
    },
    valuationItemClose() {
      this.projectStatus = false
      this.valuationItemDictNew = []
      this.valuationItemSelect = ''
    },
    valuationItemConfirm() {
      let valuation = this.valuationItemRow
      let projectRow = this.projectRowData;
      if (valuation.itemCode) {
        let tableData = this.tableData;
        let status = true;
        tableData.forEach(t => {
          if (t.chargeItemCode === valuation.itemCode) {
            t.amount += 1;
            status = false;
            this.updateProject(t)
          }
        })
        if (status) {
          let data = {
            chargeItemNo: projectRow.chargeItemNo,
            chargeItemCode: valuation.itemCode,
            chargeItemName: valuation.itemName,
            chargeItemClass: valuation.itemClass,
            chargeItemSpec: valuation.itemSpec,
            startDateTime: valuation.startDate,
            stopDataTime: valuation.stopData,
            amount: 1,
            price: valuation.price,
            units: valuation.units,
            amountInputStatus: false,
            status: false,
          }
          this.tableData.push(data)
          this.projectConfig();
        } else {
          --this.noMax;
          this.tableData.splice(this.itemRowIndex, 1)
        }
      } else {
        --this.noMax;
        this.tableData.splice(this.itemRowIndex, 1)
      }
      this.valuationItemClose();
    },
    projectResetImputedPrice() {
      this.formData.amount = this.tableData.reduce((sum, value) => sum + parseFloat(value.amount * value.price), 0).toFixed(2);
    },
    valuationItemTableClick(row) {
      this.valuationItemRow = row
    },
    valuationItemInputFilter(data) {
      if (data) {
        this.valuationItemDictNew = this.valuationItemDict.filter(t => t.itemName.includes(data) || t.inputCode.includes(data.toUpperCase()))
      } else {
        this.valuationItemDictNew = []
      }
    },
    handleRowDblClick(row, column, event) {
      this.itemRowIndex = this.tableData.indexOf(row)
      if (column.property === 'chargeItemName') {
        if (!row.status) {
          this.chargeItemClick(row);
        }
      } else if (column.property === 'amount') {
        row.amountInputStatus = true
        // 下面的代码是为了在编辑时自动聚焦输入框
        setTimeout(() => {
          this.$refs.amountRefs.focus()
        }, 200)
      }else if (column.property === 'startDateTime'){
        row.startStatus = true
        setTimeout(() => {
          this.$refs.startDateTimeRefs.focus()
        }, 200)
      }else if (column.property === 'stopDateTime'){
        row.stopStatus = true
        setTimeout(() => {
          this.$refs.stopDateTimeRefs.focus()
        }, 200)
      }
    },
    chargeItemClick(row) {
      this.projectRowData = row;
      const loading = this.$loading({
        lock: true,
        text: '数据正在努力提取中,请耐心等待!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.valuationItemRow = {};
      this.valuationItemDict = []
      this.valuationItemDictNew = []
      GetValuationDetailList(row.chargeItemClass).then(res => {
        if (res.code === 200) {
          this.valuationItemDict = res.data;
          this.projectStatus = true;
        }
      }).finally(() => {
        loading.close();
      })

    },
    addValuation() {
      let itemClass = this.formData.itemClass;
      let newItemClass = '';
      this.dataItem.forEach(x => {
        if (itemClass === x.remark){
          newItemClass = x.itemCode;
        }
      })
      let data = {
        chargeItemNo: ++this.noMax,
        chargeItemCode: '',
        chargeItemName: '',
        chargeItemClass: newItemClass,
        chargeItemSpec: '',
        amount: undefined,
        price: undefined,
        units: '',
        amountInputStatus: false,
        status: false,
      }
      this.tableData.push(data);
    },
    init(data) {
      this.formData = {
        itemCode: data.iteM_CODE,
        itemClass: data.iteM_CLASS,
        inputCode: data.inpuT_CODE,
        itemName: data.iteM_NAME,
        amount: 0,
      }
      this.type = "1";
      this.getValuationDetailByProjectCode()
      this.status = true;
    },
    getValuationDetailByProjectCode() {
      this.tableData = [];
      const loading = this.$loading({
        lock: true,
        text: '数据正在努力提取中,请耐心等待!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      GetValuationDetailByProjectCode(this.formData.itemCode, this.formData.itemClass, this.type).then(res => {
        if (res.code === 200) {
          this.tableData = res.data.dataList;
          this.dataItem = res.data.dataItem;
          this.noMax = res.data.noMax;
          this.projectResetImputedPrice();
        }
      }).finally(() => {
        loading.close();
      })
    },
  },
}
</script>

<style scoped lang="scss">
.va-contrast-home {
  ::v-deep.el-drawer__header {
    color: #ffffff !important;
    background-color: #185f7d;
    letter-spacing: 1em;
    font-size: 22px !important;
    text-align: center;
  }

  ::v-deep.el-dialog__header {
    padding: 10px !important;
    background: #185f7d !important;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  ::v-deep.el-dialog__headerbtn .el-dialog__close {
    color: #FFFFFF !important;
  }

  ::v-deep.el-dialog__title {
    line-height: 24px !important;
    font-size: 22px !important;
    color: #ffffff !important;
  }

  .va-master {
    .va-form {
      margin-top: 10px;
      display: flex;
      justify-content: center;

      .form-item {
        width: 30%;
        min-width: 210px;
        display: flex;

        .form-title {
          font-size: 22px;
          font-weight: 800;
        }

        .form-text {
          font-size: 18px;
          display: flex;
          align-items: center;
          font-weight: 500;
        }
      }
    }

    .va-item {
      margin-top: 20px;
      height: 600px;
      margin: 20px;

      .va-title {
        display: flex;
        align-items: flex-end;
        margin-left: 5px;
        font-size: 22px;
        height: 35px;
        border: #3A71A8;
        font-weight: 800;
      }

      .va-table {
        border: 1px solid #3A71A8;
        border-radius: 10px;
        height: 100%;

        .table-item {
        }
      }

    }
  }

  .dialog-master {
    //padding: 10px;
    .arrearge-master {
      padding: 10px;
      display: flex;
      flex-direction: column;

      .master-item {
        display: flex;
        align-items: center;
        height: 30px;

        .item-title {
          font-size: 18px;
          width: 100px;
        }

        .item-son {
          font-size: 16px;
          display: flex;
          align-items: center;
        }

      }

    }

    .arrearge-bottom {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 20px;
      color: #a95812;
      height: 40px;

      .arrearge-bottom-title {
        font-size: 18px;
      }

      .arrearge-bottom-text {
        font-size: 16px;
      }
    }

    .basic-information-master {
      display: flex;
      flex-direction: column;
      align-items: center;

      .basic-item {
        display: flex;
        margin-left: 10px;
        margin-bottom: 3px;

        .basic-input {
          width: 120px;

          ::v-deep.el-input.is-disabled .el-input__inner {
            background-color: #ffffff;
            border-color: #dfe4ed;
            color: #000;
            cursor: not-allowed;
          }
        }

        .basic-text {
          display: flex;
          align-items: center;
        }
      }
    }

    .project-table-master {
      margin-top: 20px;
      display: flex;

      .table-master {
        border: 1px solid #00a19b;
        width: 70%;

      }

      .button-master {
        width: 15%;
        display: flex;
        flex-direction: column;
        align-content: flex-start;
        justify-content: space-around;

        ::v-deep.el-button--medium {
          margin-left: 10px;
        }
      }
    }

    .item-table-master {
      margin-top: 10px;
      border: 1px solid #00a19b;

    }

    .bottom-button-master {
      margin-top: 10px;
      display: flex;
      justify-content: center;

      .button-item {
        display: flex;
        justify-content: space-evenly;
        width: 50%;
      }
    }

    ::v-deep.el-table--medium .el-table__cell {
      padding: 1px 0;
    }

    ::v-deep.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
      word-break: break-word;
      background-color: #f8f8f9;
      color: #515a6e;
      height: 30px;
      font-size: 13px;
    }

    ::v-deep.el-table__body tr.current-row > td.el-table__cell, .el-table__body tr.selection-row > td.el-table__cell {
      background-color: #1890FF;
      color: #FFFFFF;
    }
  }
}
</style>
