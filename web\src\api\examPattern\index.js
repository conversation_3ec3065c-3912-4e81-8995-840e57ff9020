import request from '@/utils/request'
const baseURL = "ExamRptPattern/";


// select 数据
export function GainSelectData(data) {
  return request({
    url: baseURL + "GainSelectData",
     method: 'post',
    data: data
  })
}
// 查询
export function GainExamRptPatternData(data) {
  return request({
    url: baseURL + "GainExamRptPatternData",
     method: 'post',
    data: data
  })
}
// 停用
export function RemoveExamRptPatternData(data) {
  return request({
    url: baseURL + "RemoveExamRptPatternData",
     method: 'post',
    data: data
  })
}

//新增
export function AddRptPatternData(data) {
  return request({
    url: baseURL + "AddRptPatternData",
     method: 'post',
    data: data
  })
}
//修改
export function UpdateRptPatternData(data) {
  return request({
    url: baseURL + "UpdateRptPatternData",
     method: 'post',
    data: data
  })
}
//诊疗项目
export function GetSelectDiagDict(data) {
  return request({
    url: baseURL + "GetSelectDiagDict",
     method: 'post',
    data: data
  })
}

//获取检查项目字典
export function GetExamDict(data) {
  return request({
    url: baseURL + "GetExamDict",
     method: 'post',
    data: data
  })
}
//新增检查项目字典
export function AddExamDict(data) {
  return request({
    url: baseURL + "AddExamDict",
     method: 'post',
    data: data
  })
}
//修改检查项目字典
export function UpdateExamDict(data) {
  return request({
    url: baseURL + "UpdateExamDict",
     method: 'post',
    data: data
  })
}
//科室字典
export function GetDeptDict(data) {
  return request({
    url: baseURL + "GetDeptDict",
     method: 'post',
    data: data
  })
}
